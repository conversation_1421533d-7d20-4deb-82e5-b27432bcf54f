@model MyMvcApp.ViewModels.Admin.UserCreateViewModel
@{
    ViewData["Title"] = "Tạo người dùng mới";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Tạo người dùng mới</h1>
            </div>

            
                <div class="col-md-8">
                    <div class="card shadow mb-4">
                        <div class="card-body">
                            <form asp-controller="UserManagement" asp-action="Create" method="post">
                                <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                                <div class="form-group mb-3">
                                    <label asp-for="UserName" class="control-label"></label>
                                    <input asp-for="UserName" class="form-control" />
                                    <span asp-validation-for="UserName" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="FullName" class="control-label"></label>
                                    <input asp-for="FullName" class="form-control" />
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Email" class="control-label"></label>
                                    <input asp-for="Email" class="form-control" />
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Password" class="control-label"></label>
                                    <input asp-for="Password" class="form-control" type="password" />
                                    <span asp-validation-for="Password" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="ConfirmPassword" class="control-label"></label>
                                    <input asp-for="ConfirmPassword" class="form-control" type="password" />
                                    <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="DateOfBirth" class="control-label"></label>
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Gender" class="control-label"></label>
                                    <select asp-for="Gender" class="form-control">
                                        <option value="">-- Chọn giới tính --</option>
                                        <option value="Nam">Nam</option>
                                        <option value="Nữ">Nữ</option>
                                        <option value="Khác">Khác</option>
                                    </select>
                                    <span asp-validation-for="Gender" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Role" class="control-label"></label>
                                    <select asp-for="Role" class="form-control">
                                        <option value="">-- Chọn vai trò --</option>
                                        @if (ViewBag.Roles != null)
                                        {
                                            @foreach (var role in ViewBag.Roles)
                                            {
                                                <option value="@role.Name">@role.Name</option>
                                            }
                                        }
                                    </select>
                                    <span asp-validation-for="Role" class="text-danger"></span>
                                </div>

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary">Tạo người dùng</button>
                                    <a asp-controller="UserManagement" asp-action="Index" class="btn btn-secondary">Quay lại</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
