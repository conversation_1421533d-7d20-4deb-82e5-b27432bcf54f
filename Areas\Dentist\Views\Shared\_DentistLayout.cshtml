<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - Dentist Panel - Nha Khoa 3B</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/styles.css">
    <link rel="stylesheet" href="~/css/modern-animations.css">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
            min-height: 100vh;
        }

        .main-content {
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            margin: 1rem;
            margin-left: 270px;
            margin-right: 2rem;
            max-width: calc(100vw - 320px);
            width: calc(100vw - 320px);
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            min-height: calc(100vh - 120px);
        }

        .navbar {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%) !important;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
            border-bottom: none !important;
        }

        .navbar-brand {
            color: white !important;
            font-weight: 700;
            font-size: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            margin: 0 0.25rem;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            background: rgba(255,255,255,0.1);
            transform: translateY(-1px);
        }

        .dropdown-menu {
            background: rgba(255,255,255,0.95);
            backdrop-filter: blur(10px);
            border: none;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border-radius: 10px;
        }

        .dropdown-item {
            transition: all 0.3s ease;
            border-radius: 5px;
            margin: 2px 5px;
        }

        .dropdown-item:hover {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            color: white;
            transform: translateX(5px);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            background: rgba(255,255,255,0.9);
            backdrop-filter: blur(10px);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .btn-primary {
            background: linear-gradient(135deg, #388e3c 0%, #2e7d32 100%);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(56, 142, 60, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(56, 142, 60, 0.4);
        }

        .alert {
            border: none;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .alert-success {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1) 0%, rgba(76, 175, 80, 0.05) 100%);
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }

        .alert-info {
            background: linear-gradient(135deg, rgba(33, 150, 243, 0.1) 0%, rgba(33, 150, 243, 0.05) 100%);
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }

        @@media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                margin-right: 0;
                width: 100%;
                max-width: 100%;
                border-radius: 0;
            }
        }
    </style>
    @await RenderSectionAsync("Styles", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-light bg-white border-bottom box-shadow mb-3">
            <div class="navbar-nav flex-grow-1">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Landing">
                    <i class="fas fa-user-md me-2"></i>Nha khoa 3B - Dentist Panel
                </a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex justify-content-between">
                    <ul class="navbar-nav flex-grow-1">
                        <li class="nav-item">
                            <a class="nav-link text-dark" asp-area="Dentist" asp-controller="Home" asp-action="Index">
                                <i class="fas fa-home me-1"></i>Dashboard
                            </a>
                        </li>
                    </ul>
                    @await Html.PartialAsync("_LoginPartial")
                </div>
            </div>
        </nav>
    </header>

    <!-- Dentist Sidebar -->
    @await Html.PartialAsync("~/Views/Shared/_DentistSidebar.cshtml")

    <!-- Main content -->
    <main class="main-content fade-in">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }

        @if (TempData["InfoMessage"] != null)
        {
            <div class="alert alert-info alert-dismissible fade show" role="alert">
                <i class="fas fa-info-circle me-2"></i>
                @TempData["InfoMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }

        @RenderBody()
    </main>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Custom JS -->
    <script src="~/js/script.js"></script>
    <script src="~/js/modern-effects.js"></script>
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
