@using System.Linq
@{
    ViewData["Title"] = "Trang chủ";
    Layout = "_UserLayout";
}

@section Styles {
    <link href="~/css/user-dashboard.css" rel="stylesheet">
    <style>
        .user-dashboard {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .welcome-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            margin-bottom: 15px;
        }
        
        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .action-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 15px 20px;
            color: white;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .recent-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .section-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }
    </style>
}

<div class="user-dashboard">
    <div class="container">
        <!-- Welcome Section -->
        <div class="welcome-card">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-hand-wave me-2"></i>
                        Chào mừng, @ViewBag.UserName!
                    </h1>
                    <p class="mb-0 opacity-75">
                        Chúc bạn một ngày tốt lành. Hãy theo dõi lịch hẹn và hóa đơn của bạn.
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="welcome-icon">
                        <i class="fas fa-user-circle fa-4x opacity-50"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-6">
                <div class="stats-card">
                    <div class="stats-icon bg-primary text-white">
                        <i class="fas fa-calendar-check"></i>
                    </div>
                    <div class="stats-number text-primary">@ViewBag.TotalAppointments</div>
                    <div class="text-muted">Tổng số lịch hẹn</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="stats-card">
                    <div class="stats-icon bg-success text-white">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="stats-number text-success">@(ViewBag.TotalPayments?.ToString("N0") ?? "0") VNĐ</div>
                    <div class="text-muted">Tổng chi phí</div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h4 class="section-title">
                <i class="fas fa-bolt me-2"></i>Thao tác nhanh
            </h4>
            <div class="text-center">
                <a href="@Url.Action("MyProfile", "Home")" class="action-btn">
                    <i class="fas fa-user me-2"></i>Thông tin cá nhân
                </a>
                <a href="@Url.Action("MyAppointments", "Home")" class="action-btn">
                    <i class="fas fa-calendar-alt me-2"></i>Lịch hẹn của tôi
                </a>
                <a href="@Url.Action("MyInvoices", "Home")" class="action-btn">
                    <i class="fas fa-file-invoice me-2"></i>Hóa đơn của tôi
                </a>
                <a href="@Url.Action("UpdateProfile", "Home")" class="action-btn">
                    <i class="fas fa-cog me-2"></i>Cập nhật thông tin
                </a>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="row">
            <div class="col-md-6">
                <div class="recent-section">
                    <h4 class="section-title">
                        <i class="fas fa-clock me-2"></i>Lịch hẹn gần đây
                    </h4>
                    @if (ViewBag.RecentAppointments != null && ((IEnumerable<dynamic>)ViewBag.RecentAppointments).Any())
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var appointment in (IEnumerable<dynamic>)ViewBag.RecentAppointments)
                            {
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">@appointment.Service?.Name</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                @appointment.AppointmentDate.ToString("dd/MM/yyyy HH:mm")
                                            </small>
                                        </div>
                                        <span class="badge bg-primary">@appointment.Status</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-calendar-times fa-3x mb-3"></i>
                            <p>Chưa có lịch hẹn nào</p>
                        </div>
                    }
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="recent-section">
                    <h4 class="section-title">
                        <i class="fas fa-receipt me-2"></i>Hóa đơn gần đây
                    </h4>
                    @if (ViewBag.RecentPayments != null && ((IEnumerable<dynamic>)ViewBag.RecentPayments).Any())
                    {
                        <div class="list-group list-group-flush">
                            @foreach (var payment in (IEnumerable<dynamic>)ViewBag.RecentPayments)
                            {
                                <div class="list-group-item border-0 px-0">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">@payment.Amount.ToString("N0") VNĐ</h6>
                                            <small class="text-muted">
                                                <i class="fas fa-calendar me-1"></i>
                                                @payment.PaymentDate?.ToString("dd/MM/yyyy")
                                            </small>
                                        </div>
                                        <span class="badge bg-success">@payment.Status</span>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file-invoice fa-3x mb-3"></i>
                            <p>Chưa có hóa đơn nào</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add some animation effects
            $('.stats-card').each(function(index) {
                $(this).delay(index * 100).fadeIn(500);
            });
            
            // Animate numbers
            $('.stats-number').each(function() {
                var $this = $(this);
                var countTo = parseInt($this.text().replace(/[^0-9]/g, ''));
                
                if (!isNaN(countTo)) {
                    $({ countNum: 0 }).animate({
                        countNum: countTo
                    }, {
                        duration: 1000,
                        easing: 'swing',
                        step: function() {
                            $this.text(Math.floor(this.countNum).toLocaleString());
                        },
                        complete: function() {
                            $this.text(countTo.toLocaleString());
                        }
                    });
                }
            });
        });
    </script>
}
