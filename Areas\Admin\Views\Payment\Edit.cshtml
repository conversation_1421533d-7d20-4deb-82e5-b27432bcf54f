@model MyMvcApp.Models.Payment

@{
    ViewData["Title"] = "Chỉnh sửa thanh toán";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chỉnh sửa thanh toán</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại danh sách
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />
                        <input type="hidden" asp-for="CreatedAt" />
                        <input type="hidden" asp-for="CreatedBy" />

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="PatientId" class="control-label">Bệnh nhân</label>
                                    <select asp-for="PatientId" class="form-select" id="patientSelect">
                                        <option value="">-- Chọn bệnh nhân --</option>
                                        @foreach (var patient in ViewBag.Patients)
                                        {
                                            <option value="@patient.Id">@patient.FullName</option>
                                        }
                                    </select>
                                    <span asp-validation-for="PatientId" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="ServiceId" class="control-label">Dịch vụ</label>
                                    <select asp-for="ServiceId" class="form-select" id="serviceSelect">
                                        <option value="">-- Chọn dịch vụ --</option>
                                        @foreach (var service in ViewBag.Services)
                                        {
                                            <option value="@service.Id" data-price="@service.Price">@service.Name - @service.Price.ToString("N0") VNĐ</option>
                                        }
                                    </select>
                                    <span asp-validation-for="ServiceId" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="AppointmentId" class="control-label">Lịch hẹn</label>
                                    <select asp-for="AppointmentId" class="form-select" id="appointmentSelect">
                                        <option value="">-- Chọn lịch hẹn --</option>
                                        @foreach (var appointment in ViewBag.Appointments)
                                        {
                                            <option value="@appointment.Id" data-patient-id="@appointment.PatientId">
                                                @appointment.AppointmentDate.ToString("dd/MM/yyyy") - @appointment.Patient.FullName
                                            </option>
                                        }
                                    </select>
                                    <span asp-validation-for="AppointmentId" class="text-danger"></span>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="Amount" class="control-label">Số tiền</label>
                                    <div class="input-group">
                                        <input asp-for="Amount" class="form-control" id="amountInput" />
                                        <span class="input-group-text">VNĐ</span>
                                    </div>
                                    <span asp-validation-for="Amount" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="PaymentMethod" class="control-label">Phương thức thanh toán</label>
                                    <select asp-for="PaymentMethod" class="form-select">
                                        <option value="">-- Chọn phương thức --</option>
                                        <option value="Cash">Tiền mặt</option>
                                        <option value="Credit Card">Thẻ tín dụng</option>
                                        <option value="Debit Card">Thẻ ghi nợ</option>
                                        <option value="Bank Transfer">Chuyển khoản</option>
                                        <option value="Mobile Payment">Thanh toán di động</option>
                                    </select>
                                    <span asp-validation-for="PaymentMethod" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Status" class="control-label">Trạng thái</label>
                                    <select asp-for="Status" class="form-select">
                                        <option value="Pending">Chờ xử lý</option>
                                        <option value="Completed">Hoàn thành</option>
                                        <option value="Failed">Thất bại</option>
                                        <option value="Refunded">Hoàn tiền</option>
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="TransactionId" class="control-label">Mã giao dịch</label>
                                    <input asp-for="TransactionId" class="form-control" />
                                    <span asp-validation-for="TransactionId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label asp-for="PaymentDate" class="control-label">Ngày thanh toán</label>
                                    <input asp-for="PaymentDate" class="form-control" type="datetime-local" value="@Model.PaymentDate.ToString("yyyy-MM-ddTHH:mm")" />
                                    <span asp-validation-for="PaymentDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Notes" class="control-label">Ghi chú</label>
                            <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                            <span asp-validation-for="Notes" class="text-danger"></span>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu thay đổi
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-times"></i> Hủy
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function () {
            // Tự động điền số tiền khi chọn dịch vụ
            $('#serviceSelect').change(function() {
                var selectedOption = $(this).find('option:selected');
                var price = selectedOption.data('price');
                if (price) {
                    $('#amountInput').val(price);
                }
            });

            // Tự động chọn bệnh nhân khi chọn lịch hẹn
            $('#appointmentSelect').change(function() {
                var selectedOption = $(this).find('option:selected');
                var patientId = selectedOption.data('patient-id');
                if (patientId) {
                    $('#patientSelect').val(patientId);
                }
            });
        });
    </script>
}
