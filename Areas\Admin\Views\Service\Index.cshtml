@model IEnumerable<MyMvcApp.Models.Service>

@{
    ViewData["Title"] = "Quản lý dịch vụ";
}

@section Styles {
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="~/css/admin.css" rel="stylesheet">
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Quản lý dịch vụ</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServiceModal">
                        <i class="fas fa-plus"></i> Thêm dịch vụ mới
                    </button>
                </div>
            </div>

            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            }

            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="serviceTable">
                            <thead>
                                <tr>
                                    <th>Mã</th>
                                    <th>Tên dịch vụ</th>
                                    <th>Mô tả</th>
                                    <th>Giá</th>
                                    <th>Thời gian</th>
                                    <th>Danh mục</th>
                                    <th>Trạng thái</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>@item.Id</td>
                                        <td>@item.Name</td>
                                        <td>@(item.Description?.Length > 50 ? item.Description.Substring(0, 50) + "..." : item.Description)</td>
                                        <td>@item.Price.ToString("N0") VNĐ</td>
                                        <td>@item.Duration.ToString(@"hh\:mm")</td>
                                        <td>
                                            @if (item.ServiceCategory != null)
                                            {
                                                <span class="badge bg-info">@item.ServiceCategory.Name</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-info">@item.Category</span>
                                            }
                                        </td>
                                        <td>
                                            <span class="badge @(item.IsActive ? "bg-success" : "bg-secondary") fs-6">
                                                <i class="fas @(item.IsActive ? "fa-check-circle" : "fa-times-circle") me-1"></i>
                                                @(item.IsActive ? "Đang hoạt động" : "Ngừng hoạt động")
                                            </span>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-primary" onclick="editService(@item.Id)" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="deleteService(@item.Id)" title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        

<!-- Add Service Modal -->
<div class="modal fade" id="addServiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm dịch vụ mới</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addServiceForm">
                    @Html.AntiForgeryToken()
                    <div class="mb-3">
                        <label class="form-label">Tên dịch vụ</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Giá</label>
                        <input type="number" class="form-control" name="Price" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Thời gian (phút)</label>
                        <input type="number" class="form-control" name="DurationMinutes" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Danh mục</label>
                        <select class="form-select" name="ServiceCategoryId" required>
                            <option value="">-- Chọn danh mục --</option>
                            @foreach (var category in ViewBag.ServiceCategories)
                            {
                                <option value="@category.Id">@category.Name</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="IsActive" id="isActiveNewService" checked>
                            <label class="form-check-label" for="isActiveNewService">Đang hoạt động</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="saveService()">Lưu</button>
            </div>
        </div>

<!-- Edit Service Modal -->
<div class="modal fade" id="editServiceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa dịch vụ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editServiceForm">
                    @Html.AntiForgeryToken()
                    <input type="hidden" name="Id">
                    <div class="mb-3">
                        <label class="form-label">Tên dịch vụ</label>
                        <input type="text" class="form-control" name="Name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Mô tả</label>
                        <textarea class="form-control" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Giá</label>
                        <input type="number" class="form-control" name="Price" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Thời gian (phút)</label>
                        <input type="number" class="form-control" name="DurationMinutes" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Danh mục</label>
                        <select class="form-select" name="ServiceCategoryId" required>
                            <option value="">-- Chọn danh mục --</option>
                            @foreach (var category in ViewBag.ServiceCategories)
                            {
                                <option value="@category.Id">@category.Name</option>
                            }
                        </select>
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input type="checkbox" class="form-check-input" name="IsActive" id="isActiveService">
                            <label class="form-check-label" for="isActiveService">Đang hoạt động</label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-primary" onclick="updateService()">Cập nhật</button>
            </div>
        </div>

@section Scripts {
    <!-- DataTables CSS and JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            // Check if DataTable is already initialized
            if ($.fn.DataTable.isDataTable('#serviceTable')) {
                $('#serviceTable').DataTable().destroy();
            }

            // Initialize DataTable
            $('#serviceTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']],
                pageLength: 25,
                responsive: true
            });
        });

        function saveService() {
            var formData = new FormData(document.getElementById('addServiceForm'));

            // Handle checkbox value properly
            var isActiveCheckbox = document.getElementById('isActiveNewService');
            if (isActiveCheckbox.checked) {
                formData.set('isActiveParam', 'true');
            } else {
                formData.set('isActiveParam', 'false');
            }

            $.ajax({
                url: '/Service/CreateService',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.success) {
                        $('#addServiceModal').modal('hide');
                        // Reset form
                        document.getElementById('addServiceForm').reset();
                        document.getElementById('isActiveNewService').checked = true;
                        location.reload();
                    } else {
                        var errorMessage = response.message || 'Có lỗi xảy ra';
                        if (response.errors && response.errors.length > 0) {
                            errorMessage += '\n\nChi tiết lỗi:\n' + response.errors.join('\n');
                        }
                        alert(errorMessage);
                    }
                },
                error: function (xhr, status, error) {
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu: ' + error);
                }
            });
        }

        function editService(id) {
            $.get('/Service/GetService', { id: id })
                .done(function (response) {
                    if (response.success) {
                        var form = document.getElementById('editServiceForm');
                        form.Id.value = response.id;
                        form.Name.value = response.name;
                        form.Description.value = response.description;
                        form.Price.value = response.price;
                        form.DurationMinutes.value = response.duration;
                        form.ServiceCategoryId.value = response.serviceCategoryId || '';
                        form.IsActive.checked = response.isActive;
                        $('#editServiceModal').modal('show');
                    } else {
                        alert(response.message || 'Có lỗi xảy ra');
                    }
                })
                .fail(function () {
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                });
        }

        function updateService() {
            var formData = new FormData(document.getElementById('editServiceForm'));

            // Handle checkbox value properly
            var isActiveCheckbox = document.getElementById('isActiveService');
            if (isActiveCheckbox.checked) {
                formData.set('isActiveParam', 'true');
            } else {
                formData.set('isActiveParam', 'false');
            }

            // Debug: Log form data
            console.log('UpdateService form data being sent:');
            for (var pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }

            $.ajax({
                url: '/Service/UpdateService',
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function (response) {
                    if (response.success) {
                        $('#editServiceModal').modal('hide');
                        location.reload();
                    } else {
                        var errorMessage = response.message || 'Có lỗi xảy ra';
                        if (response.errors && response.errors.length > 0) {
                            errorMessage += '\n\nChi tiết lỗi:\n' + response.errors.join('\n');
                        }
                        alert(errorMessage);
                    }
                },
                error: function (xhr, status, error) {
                    alert('Có lỗi xảy ra khi thực hiện yêu cầu: ' + error);
                }
            });
        }

        function deleteService(id) {
            if (confirm('Bạn có chắc chắn muốn xóa dịch vụ này?')) {
                var token = $('input[name="__RequestVerificationToken"]').val();
                $.post('/Service/DeleteService', {
                    id: id,
                    __RequestVerificationToken: token
                })
                    .done(function (response) {
                        if (response.success) {
                            location.reload();
                        } else {
                            alert(response.message || 'Có lỗi xảy ra');
                        }
                    })
                    .fail(function () {
                        alert('Có lỗi xảy ra khi thực hiện yêu cầu');
                    });
            }
        }
    </script>
}