@{
    ViewData["Title"] = "Thông tin phiên đăng nhập";
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4><i class="fas fa-info-circle"></i> Thông tin phiên đăng nhập</h4>
                </div>
                <div class="card-body">
                    @if (User.Identity?.IsAuthenticated == true)
                    {
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle"></i> Đ<PERSON> đăng nhập thành công!</h5>
                            <p><strong>Người dùng:</strong> @User.Identity.Name</p>
                            <p><strong>Thời gian:</strong> @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")</p>
                        </div>

                        <h6>Thông tin Cookie Authentication:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <td><strong>Tên ngư<PERSON>i dùng:</strong></td>
                                        <td>@User.Identity.Name</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Loại xác thực:</strong></td>
                                        <td>@User.Identity.AuthenticationType</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Vai trò:</strong></td>
                                        <td>
                                            @if (User.IsInRole("Admin"))
                                            {
                                                <span class="badge bg-danger">Admin</span>
                                            }
                                            @if (User.IsInRole("Staff"))
                                            {
                                                <span class="badge bg-primary">Staff</span>
                                            }
                                            @if (User.IsInRole("Dentist"))
                                            {
                                                <span class="badge bg-success">Dentist</span>
                                            }
                                            @if (User.IsInRole("User"))
                                            {
                                                <span class="badge bg-info">User</span>
                                            }
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <h6 class="mt-4">Cookies hiện tại:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Tên Cookie</th>
                                        <th>Giá trị</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var cookie in Context.Request.Cookies)
                                    {
                                        <tr>
                                            <td><code>@cookie.Key</code></td>
                                            <td>
                                                @if (cookie.Key.Contains("Auth") || cookie.Key.Contains("Session"))
                                                {
                                                    <span class="badge bg-warning">Có giá trị (ẩn vì bảo mật)</span>
                                                }
                                                else
                                                {
                                                    <small>@cookie.Value.Substring(0, Math.Min(50, cookie.Value.Length))@(cookie.Value.Length > 50 ? "..." : "")</small>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-4">
                            <h6>Hướng dẫn test Remember Me:</h6>
                            <ol>
                                <li>Đăng xuất và đăng nhập lại với checkbox "Ghi nhớ đăng nhập" được chọn</li>
                                <li>Đóng tất cả tab trình duyệt</li>
                                <li>Mở lại trình duyệt và truy cập trang web</li>
                                <li>Bạn sẽ vẫn đăng nhập mà không cần nhập lại thông tin</li>
                            </ol>
                        </div>

                        <div class="mt-3">
                            <a href="/Account/Logout" class="btn btn-danger">
                                <i class="fas fa-sign-out-alt"></i> Đăng xuất
                            </a>
                            <button onclick="location.reload()" class="btn btn-secondary">
                                <i class="fas fa-refresh"></i> Làm mới
                            </button>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle"></i> Chưa đăng nhập</h5>
                            <p>Bạn cần đăng nhập để xem thông tin phiên.</p>
                            <a href="/Account/Login" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt"></i> Đăng nhập
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
