@model MyMvcApp.Models.Patient

@{
    ViewData["Title"] = "Chỉnh sửa bệnh nhân";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chỉnh sửa bệnh nhân</h1>
            </div>

            
                <div class="col-md-8">
                    <form asp-action="Edit">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />

                        <div class="mb-3">
                            <label asp-for="FullName" class="form-label">Họ và tên</label>
                            <input asp-for="FullName" class="form-control" placeholder="Nhập họ và tên" />
                            <span asp-validation-for="FullName" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Email" class="form-label">Email</label>
                            <input asp-for="Email" class="form-control" placeholder="Nhập địa chỉ email" />
                            <span asp-validation-for="Email" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label">Số điện thoại</label>
                            <input asp-for="PhoneNumber" class="form-control" placeholder="Nhập số điện thoại" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="DateOfBirth" class="form-label">Ngày sinh</label>
                            <input asp-for="DateOfBirth" class="form-control" type="date" />
                            <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Gender" class="form-label">Giới tính</label>
                            <select asp-for="Gender" class="form-select">
                                <option value="">Chọn giới tính</option>
                                <option value="Nam">Nam</option>
                                <option value="Nữ">Nữ</option>
                                <option value="Khác">Khác</option>
                            </select>
                            <span asp-validation-for="Gender" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Address" class="form-label">Địa chỉ</label>
                            <textarea asp-for="Address" class="form-control" rows="3" placeholder="Nhập địa chỉ"></textarea>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="MedicalHistory" class="form-label">Tiền sử bệnh</label>
                            <textarea asp-for="MedicalHistory" class="form-control" rows="3" placeholder="Nhập tiền sử bệnh (nếu có)"></textarea>
                            <span asp-validation-for="MedicalHistory" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Allergies" class="form-label">Dị ứng</label>
                            <textarea asp-for="Allergies" class="form-control" rows="2" placeholder="Nhập thông tin dị ứng (nếu có)"></textarea>
                            <span asp-validation-for="Allergies" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu thay đổi
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}