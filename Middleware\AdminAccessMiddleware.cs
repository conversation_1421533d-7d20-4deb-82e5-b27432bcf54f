using Microsoft.AspNetCore.Identity;
using MyMvcApp.Models;

namespace MyMvcApp.Middleware
{
    public class AdminAccessMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<AdminAccessMiddleware> _logger;

        public AdminAccessMiddleware(RequestDelegate next, ILogger<AdminAccessMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, UserManager<ApplicationUser> userManager)
        {
            var path = context.Request.Path.Value?.ToLower();

            if (!string.IsNullOrEmpty(path))
            {
                var user = await userManager.GetUserAsync(context.User);

                // Check Admin area access
                if (IsAdminPath(path))
                {
                    if (!context.User.Identity.IsAuthenticated)
                    {
                        _logger.LogWarning("Unauthenticated user attempted to access admin path: {Path}", path);
                        context.Response.Redirect("/Account/Login?returnUrl=" + Uri.EscapeDataString(context.Request.Path));
                        return;
                    }

                    // Only Admin can access Admin area
                    if (user == null || !await userManager.IsInRoleAsync(user, "Admin"))
                    {
                        _logger.LogWarning("User {UserId} attempted to access admin path without permission: {Path}",
                            user?.Id ?? "Unknown", path);
                        context.Response.Redirect("/Account/AccessDenied");
                        return;
                    }
                }
                // Check Staff area access
                else if (IsStaffPath(path))
                {
                    if (!context.User.Identity.IsAuthenticated)
                    {
                        _logger.LogWarning("Unauthenticated user attempted to access staff path: {Path}", path);
                        context.Response.Redirect("/Account/Login?returnUrl=" + Uri.EscapeDataString(context.Request.Path));
                        return;
                    }

                    // Admin and Staff can access Staff area
                    if (user == null ||
                        (!await userManager.IsInRoleAsync(user, "Admin") &&
                         !await userManager.IsInRoleAsync(user, "Staff")))
                    {
                        _logger.LogWarning("User {UserId} attempted to access staff path without permission: {Path}",
                            user?.Id ?? "Unknown", path);
                        context.Response.Redirect("/Account/AccessDenied");
                        return;
                    }
                }
                // Check Dentist area access
                else if (IsDentistPath(path))
                {
                    if (!context.User.Identity.IsAuthenticated)
                    {
                        _logger.LogWarning("Unauthenticated user attempted to access dentist path: {Path}", path);
                        context.Response.Redirect("/Account/Login?returnUrl=" + Uri.EscapeDataString(context.Request.Path));
                        return;
                    }

                    // Admin and Dentist can access Dentist area
                    if (user == null ||
                        (!await userManager.IsInRoleAsync(user, "Admin") &&
                         !await userManager.IsInRoleAsync(user, "Dentist")))
                    {
                        _logger.LogWarning("User {UserId} attempted to access dentist path without permission: {Path}",
                            user?.Id ?? "Unknown", path);
                        context.Response.Redirect("/Account/AccessDenied");
                        return;
                    }
                }
            }

            await _next(context);
        }

        private static bool IsAdminPath(string? path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            var adminPaths = new[]
            {
                "/admin" // Areas/Admin - Only for Admin role
            };

            // Specific admin paths
            var specificAdminPaths = new[]
            {
                "/account/register", // Only admin can create users
                "/account/usermanagement"
            };

            return adminPaths.Any(adminPath => path.StartsWith(adminPath, StringComparison.OrdinalIgnoreCase)) ||
                   specificAdminPaths.Any(adminPath => path.Equals(adminPath, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsStaffPath(string? path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            var staffPaths = new[]
            {
                "/staff" // Areas/Staff - For Admin and Staff roles
            };

            return staffPaths.Any(staffPath => path.StartsWith(staffPath, StringComparison.OrdinalIgnoreCase));
        }

        private static bool IsDentistPath(string? path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            var dentistPaths = new[]
            {
                "/dentist" // Areas/Dentist - For Admin and Dentist roles
            };

            return dentistPaths.Any(dentistPath => path.StartsWith(dentistPath, StringComparison.OrdinalIgnoreCase));
        }
    }

    public static class AdminAccessMiddlewareExtensions
    {
        public static IApplicationBuilder UseAdminAccess(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<AdminAccessMiddleware>();
        }
    }
}
