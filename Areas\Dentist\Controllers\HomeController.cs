using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using MyMvcApp.Models;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.Data;
using MyMvcApp.Services.Interfaces;

namespace MyMvcApp.Areas.Dentist.Controllers
{
    [Area("Dentist")]
    [Authorize(Roles = "Admin,Dentist")]
    public class HomeController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;

        public HomeController(
            UserManager<ApplicationUser> userManager,
            ApplicationDbContext context,
            IUserService userService)
        {
            _userManager = userManager;
            _context = context;
            _userService = userService;
        }

        public async Task<IActionResult> Index()
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            
            // Get dashboard statistics for Dentist
            ViewBag.MyTodayAppointments = await _context.Appointments
                .CountAsync(a => a.DentistId == currentUser.Id && a.AppointmentDate.Date == DateTime.Today);
            
            ViewBag.MyThisWeekAppointments = await _context.Appointments
                .CountAsync(a => a.DentistId == currentUser.Id && 
                    a.AppointmentDate.Date >= DateTime.Today.AddDays(-(int)DateTime.Today.DayOfWeek) &&
                    a.AppointmentDate.Date <= DateTime.Today.AddDays(6 - (int)DateTime.Today.DayOfWeek));
            
            ViewBag.MyCompletedAppointments = await _context.Appointments
                .CountAsync(a => a.DentistId == currentUser.Id && a.Status == "Completed");
            
            ViewBag.MyPatientsCount = await _context.Appointments
                .Where(a => a.DentistId == currentUser.Id)
                .Select(a => a.PatientId)
                .Distinct()
                .CountAsync();

            // Get my recent appointments
            var myRecentAppointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Where(a => a.DentistId == currentUser.Id)
                .OrderByDescending(a => a.AppointmentDate)
                .Take(10)
                .Select(a => new
                {
                    Date = a.AppointmentDate,
                    Time = a.StartTime,
                    PatientName = a.Patient.FullName,
                    ServiceName = a.Service.Name,
                    Status = a.Status
                })
                .ToListAsync();

            ViewBag.MyRecentAppointments = myRecentAppointments;

            return View();
        }

        public IActionResult RoleTest()
        {
            return View();
        }
    }
}
