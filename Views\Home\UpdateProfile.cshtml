@model MyMvcApp.ViewModels.UserProfileUpdateViewModel
@{
    ViewData["Title"] = "Cập nhật thông tin cá nhân";
    Layout = "_UserLayout";
}

@section Styles {
    <style>
        .profile-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }

        .profile-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .profile-header {
            background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }

        .profile-body {
            padding: 40px;
        }
        
        .info-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #4A9F9F;
        }

        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .form-floating {
            margin-bottom: 20px;
        }

        .form-floating input,
        .form-floating select,
        .form-floating textarea {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .form-floating input:focus,
        .form-floating select:focus,
        .form-floating textarea:focus {
            border-color: #4A9F9F;
            box-shadow: 0 0 0 0.2rem rgba(74, 159, 159, 0.25);
        }

        .action-buttons {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
        }

        .btn-custom {
            background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 159, 159, 0.3);
            color: white;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #4A9F9F;
            border-radius: 10px;
            color: #155724;
            margin-bottom: 20px;
        }

        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .section-title {
            color: #4A9F9F;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .gender-options {
            display: flex;
            gap: 20px;
            margin-top: 10px;
        }
        
        .gender-option {
            flex: 1;
        }
        
        .gender-option input[type="radio"] {
            display: none;
        }
        
        .gender-option label {
            display: block;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .gender-option input[type="radio"]:checked + label {
            border-color: #4A9F9F;
            background: rgba(74, 159, 159, 0.1);
            color: #2E7D7D;
            font-weight: 600;
        }
        
        .gender-option label:hover {
            border-color: #4A9F9F;
            background: rgba(74, 159, 159, 0.05);
        }
        
        @@media (max-width: 768px) {
            .profile-body {
                padding: 20px;
            }

            .gender-options {
                flex-direction: column;
                gap: 10px;
            }

            .btn-custom {
                display: block;
                margin: 10px auto;
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
}

<div class="profile-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user-edit"></i>
                        </div>
                        <h2>Cập nhật thông tin cá nhân</h2>
                        <p class="mb-0">Chỉnh sửa thông tin cá nhân của bạn</p>
                    </div>

                    <div class="profile-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-success" role="alert">
                        <i class="fas fa-check-circle me-2"></i>
                        @TempData["SuccessMessage"]
                    </div>
                }
                
                <form id="updateForm" asp-action="UpdateProfile" method="post">
                    <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                    
                    <input type="hidden" asp-for="Id" />
                    
                    <!-- Thông tin cơ bản -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>Thông tin cơ bản
                        </h4>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="FullName" class="form-control" placeholder="Họ và tên" />
                                    <label asp-for="FullName">Họ và tên</label>
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="Email" class="form-control" placeholder="Email" type="email" />
                                    <label asp-for="Email">Email</label>
                                    <span asp-validation-for="Email" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="PhoneNumber" class="form-control" placeholder="Số điện thoại" />
                                    <label asp-for="PhoneNumber">Số điện thoại</label>
                                    <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input asp-for="DateOfBirth" class="form-control" type="date" />
                                    <label asp-for="DateOfBirth">Ngày sinh</label>
                                    <span asp-validation-for="DateOfBirth" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Giới tính -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-venus-mars me-2"></i>Giới tính
                        </h4>
                        
                        <div class="gender-options">
                            <div class="gender-option">
                                <input type="radio" asp-for="Gender" value="Nam" id="male" />
                                <label for="male">
                                    <i class="fas fa-mars me-2"></i>Nam
                                </label>
                            </div>
                            <div class="gender-option">
                                <input type="radio" asp-for="Gender" value="Nữ" id="female" />
                                <label for="female">
                                    <i class="fas fa-venus me-2"></i>Nữ
                                </label>
                            </div>
                            <div class="gender-option">
                                <input type="radio" asp-for="Gender" value="Khác" id="other" />
                                <label for="other">
                                    <i class="fas fa-genderless me-2"></i>Khác
                                </label>
                            </div>
                        </div>
                        <span asp-validation-for="Gender" class="text-danger"></span>
                    </div>
                    
                    <!-- Địa chỉ -->
                    <div class="form-section">
                        <h4 class="section-title">
                            <i class="fas fa-map-marker-alt me-2"></i>Địa chỉ
                        </h4>
                        
                        <div class="form-floating">
                            <textarea asp-for="Address" class="form-control" placeholder="Địa chỉ" style="height: 100px"></textarea>
                            <label asp-for="Address">Địa chỉ</label>
                            <span asp-validation-for="Address" class="text-danger"></span>
                        </div>
                    </div>
                    
                </form>

                <!-- Action Buttons -->
                <div class="action-buttons">
                    <button type="submit" form="updateForm" class="btn-custom">
                        <i class="fas fa-save me-2"></i>Cập nhật thông tin
                    </button>
                    <a href="@Url.Action("MyProfile", "Home")" class="btn-custom">
                        <i class="fas fa-arrow-left me-2"></i>Quay lại
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function() {
            // Add fade-in animation
            $('.update-profile-card').hide().fadeIn(800);
            
            // Form validation styling
            $('input, select, textarea').on('blur', function() {
                if ($(this).val()) {
                    $(this).addClass('has-value');
                } else {
                    $(this).removeClass('has-value');
                }
            });
            
            // Initialize has-value class for pre-filled inputs
            $('input, select, textarea').each(function() {
                if ($(this).val()) {
                    $(this).addClass('has-value');
                }
            });
        });
    </script>
}
