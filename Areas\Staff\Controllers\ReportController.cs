using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MyMvcApp.Data;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.ViewModels.Admin;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace MyMvcApp.Areas.Staff.Controllers
{
    [Area("Staff")]
    [Authorize(Roles = "Admin,Staff")]
    public class ReportController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;

        public ReportController(ApplicationDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public IActionResult Index()
        {
            return View();
        }

        public async Task<IActionResult> Revenue(DateTime? startDate, DateTime? endDate)
        {
            // Default to current month if no dates provided
            startDate ??= new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            endDate ??= DateTime.Now;

            var revenueData = await _context.PaymentTransactions
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Service)
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Patient)
                .Where(p => p.Status == "Completed" &&
                           p.CompletedAt.HasValue &&
                           p.CompletedAt.Value.Date >= startDate.Value.Date &&
                           p.CompletedAt.Value.Date <= endDate.Value.Date)
                .GroupBy(p => p.CompletedAt.Value.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    TotalRevenue = g.Sum(p => p.Amount),
                    TransactionCount = g.Count(),
                    Services = g.Select(p => p.Appointment.Service.Name).Distinct().ToList()
                })
                .OrderBy(r => r.Date)
                .ToListAsync();

            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.TotalRevenue = revenueData.Sum(r => r.TotalRevenue);
            ViewBag.TotalTransactions = revenueData.Sum(r => r.TransactionCount);

            return View(revenueData);
        }

        public async Task<IActionResult> Appointments(DateTime? startDate, DateTime? endDate)
        {
            startDate ??= DateTime.Today.AddMonths(-1);
            endDate ??= DateTime.Today;

            var appointmentData = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.AppointmentDate.Date >= startDate.Value.Date && 
                           a.AppointmentDate.Date <= endDate.Value.Date)
                .GroupBy(a => a.AppointmentDate.Date)
                .Select(g => new
                {
                    Date = g.Key,
                    TotalAppointments = g.Count(),
                    CompletedAppointments = g.Count(a => a.Status == "Completed"),
                    CancelledAppointments = g.Count(a => a.Status == "Cancelled"),
                    NoShowAppointments = g.Count(a => a.Status == "No-show"),
                    PendingAppointments = g.Count(a => a.Status == "Scheduled")
                })
                .OrderBy(r => r.Date)
                .ToListAsync();

            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.TotalAppointments = appointmentData.Sum(r => r.TotalAppointments);
            ViewBag.CompletedAppointments = appointmentData.Sum(r => r.CompletedAppointments);

            return View(appointmentData);
        }

        public async Task<IActionResult> Services(DateTime? startDate, DateTime? endDate)
        {
            startDate ??= DateTime.Today.AddMonths(-1);
            endDate ??= DateTime.Today;

            var serviceData = await _context.Services
                .Where(s => _context.Appointments.Any(a =>
                    a.ServiceId == s.Id &&
                    a.AppointmentDate.Date >= startDate.Value.Date &&
                    a.AppointmentDate.Date <= endDate.Value.Date &&
                    a.Status == "Completed"))
                .Select(s => new
                {
                    ServiceId = s.Id,
                    ServiceName = s.Name,
                    ServicePrice = s.Price,
                    TotalBookings = _context.Appointments.Count(a =>
                        a.ServiceId == s.Id &&
                        a.AppointmentDate.Date >= startDate.Value.Date &&
                        a.AppointmentDate.Date <= endDate.Value.Date &&
                        a.Status == "Completed"),
                    TotalRevenue = _context.PaymentTransactions
                        .Where(p => p.Status == "Completed" &&
                               _context.Appointments.Any(a =>
                                   a.Id == p.AppointmentId &&
                                   a.ServiceId == s.Id &&
                                   a.AppointmentDate.Date >= startDate.Value.Date &&
                                   a.AppointmentDate.Date <= endDate.Value.Date))
                        .Sum(p => (decimal?)p.Amount) ?? 0,
                    AverageRating = 0 // Placeholder for future rating system
                })
                .OrderByDescending(s => s.TotalBookings)
                .ToListAsync();

            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.TotalServices = serviceData.Count;
            ViewBag.TotalRevenue = serviceData.Sum(s => s.TotalRevenue);

            return View(serviceData);
        }

        public async Task<IActionResult> Patients(DateTime? startDate, DateTime? endDate)
        {
            startDate ??= DateTime.Today.AddMonths(-1);
            endDate ??= DateTime.Today;

            var patientData = await _context.Patients
                .Include(p => p.Appointments)
                .Where(p => p.Appointments.Any(a =>
                    a.AppointmentDate.Date >= startDate.Value.Date &&
                    a.AppointmentDate.Date <= endDate.Value.Date))
                .Select(p => new
                {
                    PatientId = p.Id,
                    PatientName = p.FullName,
                    Email = p.Email,
                    PhoneNumber = p.PhoneNumber, // Use PhoneNumber instead of Phone
                    TotalAppointments = p.Appointments.Count(a =>
                        a.AppointmentDate.Date >= startDate.Value.Date &&
                        a.AppointmentDate.Date <= endDate.Value.Date),
                    CompletedAppointments = p.Appointments.Count(a =>
                        a.AppointmentDate.Date >= startDate.Value.Date &&
                        a.AppointmentDate.Date <= endDate.Value.Date &&
                        a.Status == "Completed"),
                    TotalSpent = _context.PaymentTransactions
                        .Where(pt => pt.Status == "Completed" &&
                               _context.Appointments.Any(a => a.Id == pt.AppointmentId &&
                                   a.PatientId == p.Id &&
                                   a.AppointmentDate.Date >= startDate.Value.Date &&
                                   a.AppointmentDate.Date <= endDate.Value.Date))
                        .Sum(pt => (decimal?)pt.Amount) ?? 0,
                    LastVisit = p.Appointments
                        .Where(a => a.Status == "Completed")
                        .Max(a => (DateTime?)a.AppointmentDate)
                })
                .OrderByDescending(p => p.TotalSpent)
                .ToListAsync();

            ViewBag.StartDate = startDate;
            ViewBag.EndDate = endDate;
            ViewBag.TotalPatients = patientData.Count();
            ViewBag.TotalRevenue = patientData.Sum(p => p.TotalSpent);

            return View(patientData);
        }

        public async Task<IActionResult> Dashboard()
        {
            var today = DateTime.Today;
            var thisMonth = new DateTime(today.Year, today.Month, 1);
            var lastMonth = thisMonth.AddMonths(-1);

            // Today's statistics
            ViewBag.TodayAppointments = await _context.Appointments
                .CountAsync(a => a.AppointmentDate.Date == today);

            ViewBag.TodayRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue && p.CompletedAt.Value.Date == today)
                .SumAsync(p => p.Amount);

            // This month's statistics
            ViewBag.MonthAppointments = await _context.Appointments
                .CountAsync(a => a.AppointmentDate >= thisMonth);

            ViewBag.MonthRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue && p.CompletedAt.Value >= thisMonth)
                .SumAsync(p => p.Amount);

            // Last month's statistics for comparison
            ViewBag.LastMonthRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue &&
                           p.CompletedAt.Value >= lastMonth && p.CompletedAt.Value < thisMonth)
                .SumAsync(p => p.Amount);

            // Top services this month
            var topServices = await _context.Appointments
                .Include(a => a.Service)
                .Where(a => a.AppointmentDate >= thisMonth && a.Status == "Completed")
                .GroupBy(a => a.Service.Name)
                .Select(g => new { ServiceName = g.Key, Count = g.Count() })
                .OrderByDescending(s => s.Count)
                .Take(5)
                .ToListAsync();

            ViewBag.TopServices = topServices;

            // Recent activities
            var recentActivities = await _context.Activities
                .Include(a => a.User)
                .OrderByDescending(a => a.Time)
                .Take(10)
                .ToListAsync();

            ViewBag.RecentActivities = recentActivities;

            return View();
        }

        [HttpGet]
        public async Task<IActionResult> ExportRevenue(DateTime? startDate, DateTime? endDate)
        {
            startDate ??= new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            endDate ??= DateTime.Now;

            var revenueData = await _context.PaymentTransactions
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Service)
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Patient)
                .Where(p => p.Status == "Completed" &&
                           p.CompletedAt.HasValue &&
                           p.CompletedAt.Value.Date >= startDate.Value.Date &&
                           p.CompletedAt.Value.Date <= endDate.Value.Date)
                .OrderBy(p => p.CompletedAt)
                .ToListAsync();

            // Generate CSV content
            var csv = "Ngày,Bệnh nhân,Dịch vụ,Số tiền,Trạng thái\n";
            foreach (var payment in revenueData)
            {
                csv += $"{payment.CompletedAt:dd/MM/yyyy},{payment.Appointment?.Patient?.FullName},{payment.Appointment?.Service?.Name},{payment.Amount:N0},{payment.Status}\n";
            }

            var bytes = System.Text.Encoding.UTF8.GetBytes(csv);
            return File(bytes, "text/csv", $"BaoCaoDoanhThu_{startDate:yyyyMMdd}_{endDate:yyyyMMdd}.csv");
        }
    }
}
