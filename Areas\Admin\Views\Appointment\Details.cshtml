@model MyMvcApp.Models.Appointment
@{
    ViewData["Title"] = "Chi tiết lịch hẹn";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chi tiết lịch hẹn</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Chỉnh sửa
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4"><PERSON><PERSON> lịch hẹn</dt>
                                <dd class="col-sm-8">@Model.Id</dd>

                                <dt class="col-sm-4">Bệnh nhân</dt>
                                <dd class="col-sm-8">@Model.Patient?.FullName</dd>

                                <dt class="col-sm-4">Dịch vụ</dt>
                                <dd class="col-sm-8">@Model.Service?.Name</dd>

                                <dt class="col-sm-4">Nha sĩ</dt>
                                <dd class="col-sm-8">@Model.Dentist?.FullName</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Ngày hẹn</dt>
                                <dd class="col-sm-8">@Model.AppointmentDate.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Giờ bắt đầu</dt>
                                <dd class="col-sm-8">@Model.StartTime.ToString(@"hh\:mm")</dd>

                                <dt class="col-sm-4">Giờ kết thúc</dt>
                                <dd class="col-sm-8">@Model.EndTime.ToString(@"hh\:mm")</dd>

                                <dt class="col-sm-4">Trạng thái</dt>
                                <dd class="col-sm-8">
                                    @switch (Model.Status)
                                    {
                                        case "Scheduled":
                                            <span class="badge bg-primary">Đã lên lịch</span>
                                            break;
                                        case "Completed":
                                            <span class="badge bg-success">Đã hoàn thành</span>
                                            break;
                                        case "Cancelled":
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                        case "No-show":
                                            <span class="badge bg-warning">Không đến</span>
                                            break;
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Ghi chú</h5>
                            <p class="text-muted">@(string.IsNullOrEmpty(Model.Notes) ? "Không có ghi chú" : Model.Notes)</p>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Thông tin bổ sung</h5>
                            <dl class="row">
                                <dt class="col-sm-3">Người tạo</dt>
                                <dd class="col-sm-9">@Model.CreatedByUser?.FullName</dd>

                                <dt class="col-sm-3">Ngày tạo</dt>
                                <dd class="col-sm-9">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>

                                @if (Model.UpdatedAt.HasValue)
                                {
                                    <dt class="col-sm-3">Cập nhật lần cuối</dt>
                                    <dd class="col-sm-9">@Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm")</dd>
                                }
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        
</div> 