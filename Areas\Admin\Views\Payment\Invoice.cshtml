@model MyMvcApp.Models.Appointment

@{
    ViewData["Title"] = "Hóa đơn thanh toán";
    Layout = null; // Không sử dụng layout để in
    var transaction = ViewBag.Transaction as MyMvcApp.Models.PaymentTransaction;
}

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background-color: #f8f9fa;
        }

        .invoice-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .invoice-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .invoice-body {
            padding: 30px;
        }

        .info-section {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background-color: #f8f9fa;
        }

        .info-section h5 {
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .total-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            font-size: 1.2em;
            font-weight: bold;
        }

        .print-btn {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        @@media print {
            .print-btn {
                display: none;
            }

            .invoice-container {
                box-shadow: none;
                margin: 0;
            }

            body {
                background-color: white;
            }
        }

        .qr-section {
            text-align: center;
            padding: 20px;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <button class="btn btn-primary print-btn" onclick="window.print()">
        <i class="fas fa-print"></i> In hóa đơn
    </button>

    <div class="invoice-container">
        <div class="invoice-header">
            <h1><i class="fas fa-tooth"></i> PHÒNG KHÁM NHA KHOA</h1>
            <h2>HÓA ĐƠN THANH TOÁN</h2>
            <p>Số hóa đơn: #@Model.Id - @DateTime.Now.ToString("yyyyMMdd")</p>
        </div>

        <div class="invoice-body">
            <!-- Thông tin bệnh nhân -->
            <div class="info-section">
                <h5><i class="fas fa-user"></i> Thông tin bệnh nhân</h5>
                
                    <div class="col-md-6">
                        <p><strong>Họ tên:</strong> @Model.Patient.FullName</p>
                        <p><strong>Email:</strong> @Model.Patient.Email</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Số điện thoại:</strong> @Model.Patient.PhoneNumber</p>
                        <p><strong>Địa chỉ:</strong> @(Model.Patient.Address ?? "Không có")</p>
                    </div>
                </div>
            </div>

            <!-- Thông tin dịch vụ -->
            <div class="info-section">
                <h5><i class="fas fa-medical-kit"></i> Thông tin dịch vụ</h5>
                
                    <div class="col-md-6">
                        <p><strong>Dịch vụ:</strong> @Model.Service.Name</p>
                        <p><strong>Mô tả:</strong> @(Model.Service.Description ?? "Không có")</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Bác sĩ điều trị:</strong> @Model.Dentist.FullName</p>
                        <p><strong>Ngày khám:</strong> @Model.AppointmentDate.ToString("dd/MM/yyyy")</p>
                        <p><strong>Giờ khám:</strong> @Model.StartTime.ToString(@"hh\:mm") - @Model.EndTime.ToString(@"hh\:mm")</p>
                    </div>
                </div>
            </div>

            <!-- Bảng chi tiết -->
            <div class="info-section">
                <h5><i class="fas fa-list"></i> Chi tiết thanh toán</h5>
                <table class="table table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th>Dịch vụ</th>
                            <th>Số lượng</th>
                            <th>Đơn giá</th>
                            <th>Thành tiền</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>@Model.Service.Name</td>
                            <td>1</td>
                            <td>@Model.Service.Price.ToString("N0") VNĐ</td>
                            <td>@Model.Service.Price.ToString("N0") VNĐ</td>
                        </tr>
                    </tbody>
                </table>

                @if (transaction != null)
                {
                    <div class="mt-3">
                        <h6><i class="fas fa-credit-card"></i> Thông tin giao dịch:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Mã giao dịch:</strong> @transaction.OrderId</p>
                                <p><strong>Phương thức:</strong> @transaction.PaymentMethod</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Ngày thanh toán:</strong> @transaction.CompletedAt?.ToString("dd/MM/yyyy HH:mm")</p>
                                <p><strong>Trạng thái:</strong> <span class="badge bg-success">Đã thanh toán</span></p>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- Tổng tiền -->
            <div class="total-section">
                <i class="fas fa-calculator"></i>
                TỔNG TIỀN: @Model.Service.Price.ToString("N0") VNĐ
            </div>

            <!-- QR Code cho thanh toán -->
            <div class="qr-section">
                <h6><i class="fas fa-qrcode"></i> Mã QR thanh toán đã hoàn thành</h6>
                <div id="qrcode-container" class="text-center">
                    <!-- QR Code sẽ được tạo ở đây -->
                </div>
                <p class="text-muted mt-2 text-center">Mã QR đã được sử dụng để thanh toán hóa đơn này</p>
            </div>

            <!-- Thông tin phòng khám -->
            <div class="info-section">
                <h5><i class="fas fa-hospital"></i> Thông tin phòng khám</h5>
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Tên phòng khám:</strong> Phòng khám Nha khoa ABC</p>
                        <p><strong>Địa chỉ:</strong> 123 Đường ABC, Quận 1, TP.HCM</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Điện thoại:</strong> (028) 1234 5678</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Website:</strong> www.nhakhoa.com</p>
                    </div>
                </div>
            </div>

            <!-- Lời cảm ơn -->
            <div class="text-center mt-4">
                <div class="alert alert-success">
                    <h5><i class="fas fa-heart"></i> Cảm ơn quý khách!</h5>
                    <p class="mb-0">Cảm ơn quý khách đã tin tưởng và sử dụng dịch vụ của chúng tôi.
                    Chúc quý khách sức khỏe tốt!</p>
                </div>
            </div>

            <!-- Nút hành động -->
            <div class="action-buttons text-center mt-4 mb-4" style="page-break-inside: avoid;">
                <button onclick="window.print()" class="btn btn-primary me-2">
                    <i class="fas fa-print"></i> In hóa đơn
                </button>
                <a href="@Url.Action("DownloadInvoicePDF", "Payment", new { appointmentId = Model.Id })" class="btn btn-success me-2">
                    <i class="fas fa-download"></i> Tải hóa đơn
                </a>
                <a href="@Url.Action("Index", "Payment")" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left"></i> Quay lại
                </a>
                <button onclick="shareInvoice()" class="btn btn-info">
                    <i class="fas fa-share"></i> Chia sẻ
                </button>
            </div>

            <!-- Chữ ký -->
            <div class="row mt-4">
                <div class="col-md-6 text-center">
                    <p><strong>Bệnh nhân</strong></p>
                    <p style="margin-top: 60px;">(@Model.Patient.FullName)</p>
                </div>
                <div class="col-md-6 text-center">
                    <p><strong>Bác sĩ điều trị</strong></p>
                    <p style="margin-top: 60px;">(@Model.Dentist.FullName)</p>
                </div>
            </div>

            <div class="text-center mt-3">
                <small class="text-muted">
                    Hóa đơn được tạo tự động vào lúc @DateTime.Now.ToString("dd/MM/yyyy HH:mm:ss")
                </small>
            </div>
        </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Tạo QR Code cho hóa đơn đã thanh toán
        document.addEventListener('DOMContentLoaded', function() {
            const qrContainer = document.getElementById('qrcode-container');

            // Tạo dữ liệu QR cho hóa đơn
            const invoiceData = `INVOICE:@Model.Id:@Model.Patient.FullName:@Model.Service.Price:PAID:@DateTime.Now.ToString("yyyyMMdd")`;

            // Sử dụng QR Server API để tạo QR code
            const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=${encodeURIComponent(invoiceData)}`;

            qrContainer.innerHTML = `
                <img src="${qrServerUrl}" alt="Invoice QR Code" class="img-fluid" style="max-width: 150px;"
                     onload="console.log('Invoice QR Code loaded')"
                     onerror="showQRError()">
            `;
        });

        function showQRError() {
            const qrContainer = document.getElementById('qrcode-container');
            qrContainer.innerHTML = '<p class="text-muted"><i class="fas fa-exclamation-triangle"></i> Không thể tạo mã QR</p>';
        }

        function shareInvoice() {
            if (navigator.share) {
                navigator.share({
                    title: 'Hóa đơn thanh toán - Phòng khám nha khoa',
                    text: 'Hóa đơn thanh toán cho @Model.Patient.FullName - @Model.Service.Name',
                    url: window.location.href
                }).then(() => {
                    console.log('Chia sẻ thành công');
                }).catch((error) => {
                    console.log('Lỗi chia sẻ:', error);
                    fallbackShare();
                });
            } else {
                fallbackShare();
            }
        }

        function fallbackShare() {
            const url = window.location.href;
            const text = `Hóa đơn thanh toán - @Model.Patient.FullName - @Model.Service.Name\n${url}`;

            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    alert('Đã copy link hóa đơn vào clipboard!');
                }).catch(() => {
                    showShareModal(text);
                });
            } else {
                showShareModal(text);
            }
        }

        function showShareModal(text) {
            const modal = document.createElement('div');
            modal.innerHTML = `
                <div class="modal fade" id="shareModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Chia sẻ hóa đơn</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>Copy link sau để chia sẻ:</p>
                                <textarea class="form-control" rows="3" readonly>${text}</textarea>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
            const shareModal = new bootstrap.Modal(document.getElementById('shareModal'));
            shareModal.show();
        }

        // Ẩn nút hành động khi in
        window.addEventListener('beforeprint', function() {
            document.querySelector('.action-buttons').style.display = 'none';
        });

        window.addEventListener('afterprint', function() {
            document.querySelector('.action-buttons').style.display = 'block';
        });
    </script>
</body>
</html>
