using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.Data;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using System.Linq;

namespace MyMvcApp.Areas.Dentist.Controllers
{
    [Area("Dentist")]
    [Authorize(Roles = "Admin,Dentist")]
    public class AppointmentController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;
        private readonly UserManager<ApplicationUser> _userManager;

        public AppointmentController(
            ApplicationDbContext context, 
            IUserService userService,
            UserManager<ApplicationUser> userManager)
        {
            _context = context;
            _userService = userService;
            _userManager = userManager;
        }

        public async Task<IActionResult> Index()
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.DentistId == currentUser.Id) // Only show dentist's appointments
                .OrderByDescending(a => a.AppointmentDate)
                .ThenBy(a => a.StartTime)
                .ToListAsync();

            return View(appointments);
        }

        public async Task<IActionResult> Calendar()
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.DentistId == currentUser.Id) // Only show dentist's appointments
                .OrderBy(a => a.AppointmentDate)
                .ThenBy(a => a.StartTime)
                .ToListAsync();

            return View(appointments);
        }

        [HttpGet]
        public async Task<IActionResult> GetAppointments()
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.DentistId == currentUser.Id && a.Status != "Cancelled")
                .ToListAsync();

            var result = appointments.Select(a => new
            {
                id = a.Id,
                title = $"{a.Patient?.FullName} - {a.Service?.Name}",
                start = a.AppointmentDate.ToString("yyyy-MM-dd") + "T" + a.StartTime.ToString(@"hh\:mm\:ss"),
                end = a.AppointmentDate.ToString("yyyy-MM-dd") + "T" + a.EndTime.ToString(@"hh\:mm\:ss"),
                backgroundColor = GetColorFromStatus(a.Status),
                borderColor = GetColorFromStatus(a.Status),
                extendedProps = new
                {
                    patientName = a.Patient?.FullName,
                    serviceName = a.Service?.Name,
                    dentistName = a.Dentist?.FullName,
                    status = a.Status,
                    notes = a.Notes
                }
            }).ToList();

            return Json(result);
        }

        public async Task<IActionResult> Details(int id)
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            
            var appointment = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Include(a => a.CreatedByUser)
                .Where(a => a.DentistId == currentUser.Id) // Only show dentist's appointments
                .FirstOrDefaultAsync(a => a.Id == id);

            if (appointment == null)
            {
                return NotFound();
            }

            return View(appointment);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> MarkAsCompleted(int id)
        {
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync(User);
                
                var appointment = await _context.Appointments
                    .Where(a => a.DentistId == currentUser.Id) // Only allow dentist to update their appointments
                    .FirstOrDefaultAsync(a => a.Id == id);
                
                if (appointment == null)
                {
                    return Json(new { success = false, errors = new[] { "Không tìm thấy lịch hẹn" } });
                }

                appointment.Status = "Completed";
                appointment.UpdatedAt = DateTime.Now;
                
                _context.Update(appointment);
                await _context.SaveChangesAsync();
                
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, errors = new[] { "Có lỗi xảy ra khi cập nhật trạng thái" } });
            }
        }

        private string GetColorFromStatus(string status)
        {
            return status switch
            {
                "Completed" => "#28a745",
                "Cancelled" => "#dc3545",
                "No-show" => "#6c757d",
                _ => "#007bff"
            };
        }
    }
}
