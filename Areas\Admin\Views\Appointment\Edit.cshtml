@model MyMvcApp.Models.Appointment
@{
    ViewData["Title"] = "Chỉnh sửa lịch hẹn";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chỉnh sửa lịch hẹn</h1>
            </div>

            <div class="card">
                <div class="card-body">
                    <form asp-action="Edit" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="PatientId" class="control-label">Bệnh nhân</label>
                                    <select asp-for="PatientId" class="form-select" asp-items="ViewBag.Patients">
                                        <option value="">-- Ch<PERSON><PERSON> bệnh nhân --</option>
                                    </select>
                                    <span asp-validation-for="PatientId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="ServiceId" class="control-label">Dịch vụ</label>
                                    <select asp-for="ServiceId" class="form-select" asp-items="ViewBag.Services">
                                        <option value="">-- Chọn dịch vụ --</option>
                                    </select>
                                    <span asp-validation-for="ServiceId" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="DentistId" class="control-label">Nha sĩ</label>
                                    <select asp-for="DentistId" class="form-select" asp-items="ViewBag.Dentists">
                                        <option value="">-- Chọn nha sĩ --</option>
                                    </select>
                                    <span asp-validation-for="DentistId" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="AppointmentDate" class="control-label">Ngày hẹn</label>
                                    <input asp-for="AppointmentDate" class="form-control" type="date" />
                                    <span asp-validation-for="AppointmentDate" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="StartTime" class="control-label">Giờ bắt đầu</label>
                                    <input asp-for="StartTime" class="form-control" type="time" />
                                    <span asp-validation-for="StartTime" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="EndTime" class="control-label">Giờ kết thúc</label>
                                    <input asp-for="EndTime" class="form-control" type="time" />
                                    <span asp-validation-for="EndTime" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Status" class="control-label">Trạng thái</label>
                                    <select asp-for="Status" class="form-select">
                                        <option value="Scheduled">Đã lên lịch</option>
                                        <option value="Completed">Đã hoàn thành</option>
                                        <option value="Cancelled">Đã hủy</option>
                                        <option value="No-show">Không đến</option>
                                    </select>
                                    <span asp-validation-for="Status" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="Notes" class="control-label">Ghi chú</label>
                                    <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                                    <span asp-validation-for="Notes" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Lưu
                            </button>
                            <a asp-action="Index" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Quay lại
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function () {
            // Tự động tính giờ kết thúc dựa trên dịch vụ được chọn
            $('#ServiceId').change(function() {
                var serviceId = $(this).val();
                if (serviceId) {
                    $.get('/Appointment/GetServiceDuration', { id: serviceId }, function(duration) {
                        var startTime = $('#StartTime').val();
                        if (startTime) {
                            var start = new Date('2000-01-01T' + startTime);
                            var end = new Date(start.getTime() + duration * 60000);
                            var endTime = end.toTimeString().slice(0, 5);
                            $('#EndTime').val(endTime);
                        }
                    });
                }
            });

            // Tự động tính giờ kết thúc khi thay đổi giờ bắt đầu
            $('#StartTime').change(function() {
                var serviceId = $('#ServiceId').val();
                if (serviceId) {
                    $.get('/Appointment/GetServiceDuration', { id: serviceId }, function(duration) {
                        var startTime = $('#StartTime').val();
                        if (startTime) {
                            var start = new Date('2000-01-01T' + startTime);
                            var end = new Date(start.getTime() + duration * 60000);
                            var endTime = end.toTimeString().slice(0, 5);
                            $('#EndTime').val(endTime);
                        }
                    });
                }
            });
        });
    </script>
} 