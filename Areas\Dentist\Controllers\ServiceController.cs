using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.Data;
using System.Threading.Tasks;

namespace MyMvcApp.Areas.Dentist.Controllers
{
    [Area("Dentist")]
    [Authorize(Roles = "Admin,Dentist")]
    public class ServiceController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;

        public ServiceController(ApplicationDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        // GET: Service - Read-only view for dentists
        public async Task<IActionResult> Index()
        {
            var services = await _context.Services
                .Include(s => s.ServiceCategory)
                .Where(s => s.IsActive) // Only show active services
                .OrderBy(s => s.Category)
                .ThenBy(s => s.Name)
                .ToListAsync();

            ViewBag.IsReadOnly = true; // Flag to indicate read-only mode
            return View(services);
        }

        // GET: Service/Details/5 - View service details
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var service = await _context.Services
                .Include(s => s.ServiceCategory)
                .FirstOrDefaultAsync(s => s.Id == id);

            if (service == null)
            {
                return NotFound();
            }

            ViewBag.IsReadOnly = true;
            return View(service);
        }

        // Dentist cannot create, edit, or delete services
        public IActionResult Create()
        {
            TempData["ErrorMessage"] = "Bạn không có quyền tạo dịch vụ mới. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }

        public IActionResult Edit(int? id)
        {
            TempData["ErrorMessage"] = "Bạn không có quyền chỉnh sửa dịch vụ. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }

        public IActionResult Delete(int? id)
        {
            TempData["ErrorMessage"] = "Bạn không có quyền xóa dịch vụ. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }

        // GET: Service categories for reference
        public async Task<IActionResult> Categories()
        {
            var categories = await _context.ServiceCategories
                .Where(c => c.IsActive)
                .OrderBy(c => c.Name)
                .ToListAsync();

            ViewBag.IsReadOnly = true;
            return View(categories);
        }
    }
}
