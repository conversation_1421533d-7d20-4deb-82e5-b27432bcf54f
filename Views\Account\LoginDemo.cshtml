@{
    ViewData["Title"] = "Demo Login";
    Layout = null;
}

<!DOCTYPE html>
<html>
<head>
    <title>Demo Login - Test tài khoản</title>
    <meta charset="utf-8">
    <style>
        body { 
            font-family: Arial, sans-serif; 
            max-width: 600px; 
            margin: 50px auto; 
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .account-info {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #4CAF50;
        }
        .btn {
            background: #4A9F9F;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #2E7D7D;
            color: white;
        }
        h1 { color: #2c3e50; }
        h3 { color: #4A9F9F; }
    </style>
</head>
<body>
    <div class="demo-card">
        <h1>🦷 Demo Trang Login - Nha Khoa 3B</h1>
        
        <p>Chào mừng bạn đến với hệ thống quản lý nha khoa! Dưới đây là thông tin tài khoản demo để test:</p>
        
        <div class="account-info">
            <h3>👨‍💼 Tài khoản Admin</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Mật khẩu:</strong> Admin@123</p>
            <p><strong>Quyền:</strong> Quản trị viên - Truy cập đầy đủ tất cả chức năng</p>
        </div>

        <div class="account-info" style="background: #e3f2fd; border-left-color: #2196F3;">
            <h3>👩‍⚕️ Tài khoản Staff</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Mật khẩu:</strong> Staff@123</p>
            <p><strong>Quyền:</strong> Nhân viên - Quản lý bệnh nhân, lịch hẹn, dịch vụ</p>
        </div>

        <div class="account-info" style="background: #e8f5e8; border-left-color: #4CAF50;">
            <h3>👨‍⚕️ Tài khoản Dentist</h3>
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Mật khẩu:</strong> Dentist@123</p>
            <p><strong>Quyền:</strong> Bác sĩ - Quản lý bệnh nhân và lịch hẹn</p>
        </div>

        <h3>🔗 Liên kết nhanh:</h3>
        <a href="/login" class="btn">🔐 Đăng nhập</a>
        <a href="/Account/UserRegister" class="btn">📝 Đăng ký tài khoản mới</a>
        <a href="/Home/Landing" class="btn">🏠 Trang chủ</a>

        <hr style="margin: 30px 0;">

        <h3>🛠️ Tạo tài khoản demo (Chỉ dành cho Admin):</h3>
        <a href="/Admin/Home/CreateStaff" class="btn" style="background: #2196F3;">👩‍⚕️ Tạo tài khoản Staff</a>
        <a href="/Admin/Home/CreateDentist" class="btn" style="background: #4CAF50;">👨‍⚕️ Tạo tài khoản Dentist</a>
        
        <hr style="margin: 30px 0;">
        
        <h3>📋 Hướng dẫn sử dụng:</h3>
        <ol>
            <li><strong>Đăng nhập Admin:</strong> Sử dụng tài khoản <EMAIL> để truy cập Admin Panel</li>
            <li><strong>Đăng ký User:</strong> Tạo tài khoản người dùng mới để trải nghiệm chức năng đặt lịch hẹn</li>
            <li><strong>Quản lý hệ thống:</strong> Admin có thể quản lý người dùng, lịch hẹn, dịch vụ, thanh toán</li>
        </ol>

        <h3>✨ Tính năng chính:</h3>
        <ul>
            <li>🎨 Giao diện hiện đại, responsive</li>
            <li>🔐 Hệ thống đăng nhập/đăng ký bảo mật</li>
            <li>👥 Phân quyền người dùng (Admin, Staff, User)</li>
            <li>📅 Quản lý lịch hẹn</li>
            <li>🦷 Quản lý dịch vụ nha khoa</li>
            <li>💳 Xử lý thanh toán</li>
            <li>📊 Báo cáo và thống kê</li>
        </ul>

        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 5px; border-left: 4px solid #ffc107;">
            <p><strong>⚠️ Lưu ý:</strong> Đây là phiên bản demo. Dữ liệu có thể được reset định kỳ.</p>
        </div>
    </div>
</body>
</html>
