using System.ComponentModel.DataAnnotations;

namespace MyMvcApp.ViewModels
{
    public class ProfileViewModel
    {
        public string Id { get; set; } = string.Empty;

        [Required(ErrorMessage = "<PERSON>ui lòng nhập họ tên")]
        [Display(Name = "<PERSON>ọ tên")]
        [StringLength(100, ErrorMessage = "Họ tên không được vượt quá 100 ký tự")]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "<PERSON>ui lòng nhập email")]
        [EmailAddress(ErrorMessage = "Email không hợp lệ")]
        [Display(Name = "Email")]
        public string Email { get; set; } = string.Empty;

        [Display(Name = "Tên đăng nhập")]
        public string UserName { get; set; } = string.Empty;

        [Display(Name = "Số điện thoại")]
        [Phone(ErrorMessage = "Số điện thoại không hợp lệ")]
        public string? PhoneNumber { get; set; }

        [Display(Name = "<PERSON><PERSON><PERSON> sinh")]
        [DataType(DataType.Date)]
        public DateOnly? DateOfBirth { get; set; }

        [Display(Name = "Giới tính")]
        public string? Gender { get; set; }

        [Display(Name = "Địa chỉ")]
        [StringLength(200, ErrorMessage = "Địa chỉ không được vượt quá 200 ký tự")]
        public string? Address { get; set; }

        [Display(Name = "Vai trò")]
        public List<string> Roles { get; set; } = new List<string>();

        [Display(Name = "Ngày tạo tài khoản")]
        public DateTime CreatedAt { get; set; }

        [Display(Name = "Lần đăng nhập cuối")]
        public DateTime? LastLoginAt { get; set; }
    }
}
