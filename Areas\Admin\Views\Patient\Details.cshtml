@model MyMvcApp.Models.Patient

@{
    ViewData["Title"] = "Chi tiết bệnh nhân";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chi tiết bệnh nhân</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                        <i class="fas fa-edit"></i> Chỉnh sửa
                    </a>
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại
                    </a>
                </div>
            </div>

            
                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user"></i> Thông tin cá nhân
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Mã bệnh nhân</dt>
                                <dd class="col-sm-8">@Model.Id</dd>

                                <dt class="col-sm-4">Họ tên</dt>
                                <dd class="col-sm-8">@Model.FullName</dd>

                                <dt class="col-sm-4">Ngày sinh</dt>
                                <dd class="col-sm-8">@Model.DateOfBirth.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Giới tính</dt>
                                <dd class="col-sm-8">@Model.Gender</dd>

                                <dt class="col-sm-4">Email</dt>
                                <dd class="col-sm-8">@Model.Email</dd>

                                <dt class="col-sm-4">Số điện thoại</dt>
                                <dd class="col-sm-8">@Model.PhoneNumber</dd>

                                <dt class="col-sm-4">Địa chỉ</dt>
                                <dd class="col-sm-8">@Model.Address</dd>
                            </dl>
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-notes-medical"></i> Thông tin y tế
                            </h5>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-4">Tiền sử bệnh</dt>
                                <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.MedicalHistory) ? "Không có" : Model.MedicalHistory)</dd>

                                <dt class="col-sm-4">Dị ứng</dt>
                                <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.Allergies) ? "Không có" : Model.Allergies)</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-calendar-check"></i> Lịch sử khám
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.Appointments.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead>
                                            <tr>
                                                <th>Ngày khám</th>
                                                <th>Dịch vụ</th>
                                                <th>Trạng thái</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var appointment in Model.Appointments.OrderByDescending(a => a.AppointmentDate))
                                            {
                                                <tr>
                                                    <td>@appointment.AppointmentDate.ToString("dd/MM/yyyy")</td>
                                                    <td>@appointment.Service.Name</td>
                                                    <td>
                                                        <span class="badge @(appointment.Status == "Completed" ? "bg-success" : 
                                                                          appointment.Status == "Scheduled" ? "bg-primary" : 
                                                                          appointment.Status == "Cancelled" ? "bg-danger" : "bg-warning")">
                                                            @appointment.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="@Url.Action("Details", "Appointment", new { id = appointment.Id })" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">Chưa có lịch sử khám.</p>
                            }
                        </div>
                    </div>

                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-money-bill-wave"></i> Lịch sử thanh toán
                            </h5>
                        </div>
                        <div class="card-body">
                            @if (Model.Payments.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-sm table-hover">
                                        <thead>
                                            <tr>
                                                <th>Ngày thanh toán</th>
                                                <th>Dịch vụ</th>
                                                <th>Số tiền</th>
                                                <th>Trạng thái</th>
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var payment in Model.Payments.OrderByDescending(p => p.PaymentDate))
                                            {
                                                <tr>
                                                    <td>@payment.PaymentDate.ToString("dd/MM/yyyy")</td>
                                                    <td>@payment.Service.Name</td>
                                                    <td>@payment.Amount.ToString("N0") VNĐ</td>
                                                    <td>
                                                        <span class="badge @(payment.Status == "Completed" ? "bg-success" : 
                                                                          payment.Status == "Pending" ? "bg-warning" : 
                                                                          payment.Status == "Failed" ? "bg-danger" : "bg-info")">
                                                            @payment.Status
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <a href="@Url.Action("Details", "Payment", new { id = payment.Id })" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <p class="text-muted">Chưa có lịch sử thanh toán.</p>
                            }
                        </div>
                    </div>
                </div>
            </div>
        
