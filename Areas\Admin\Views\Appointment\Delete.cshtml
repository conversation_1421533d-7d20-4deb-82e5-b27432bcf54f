@model MyMvcApp.Models.Appointment
@{
    ViewData["Title"] = "Xóa lịch hẹn";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Xóa lịch hẹn</h1>
            </div>

            <div class="alert alert-danger">
                <h4 class="alert-heading">Cảnh báo!</h4>
                <p>Bạn có chắc chắn muốn xóa lịch hẹn này? Hành động này không thể hoàn tác.</p>
            </div>

            <div class="card">
                <div class="card-body">
                    
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Mã lịch hẹn</dt>
                                <dd class="col-sm-8">@Model.Id</dd>

                                <dt class="col-sm-4"><PERSON>ệnh nhân</dt>
                                <dd class="col-sm-8">@Model.Patient?.FullName</dd>

                                <dt class="col-sm-4">Dịch vụ</dt>
                                <dd class="col-sm-8">@Model.Service?.Name</dd>

                                <dt class="col-sm-4">Nha sĩ</dt>
                                <dd class="col-sm-8">@Model.Dentist?.FullName</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Ngày hẹn</dt>
                                <dd class="col-sm-8">@Model.AppointmentDate.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Giờ bắt đầu</dt>
                                <dd class="col-sm-8">@Model.StartTime.ToString(@"hh\:mm")</dd>

                                <dt class="col-sm-4">Giờ kết thúc</dt>
                                <dd class="col-sm-8">@Model.EndTime.ToString(@"hh\:mm")</dd>

                                <dt class="col-sm-4">Trạng thái</dt>
                                <dd class="col-sm-8">
                                    @switch (Model.Status)
                                    {
                                        case "Scheduled":
                                            <span class="badge bg-primary">Đã lên lịch</span>
                                            break;
                                        case "Completed":
                                            <span class="badge bg-success">Đã hoàn thành</span>
                                            break;
                                        case "Cancelled":
                                            <span class="badge bg-danger">Đã hủy</span>
                                            break;
                                        case "No-show":
                                            <span class="badge bg-warning">Không đến</span>
                                            break;
                                    }
                                </dd>
                            </dl>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>Ghi chú</h5>
                            <p class="text-muted">@(string.IsNullOrEmpty(Model.Notes) ? "Không có ghi chú" : Model.Notes)</p>
                        </div>
                    </div>

                    <form asp-action="Delete" method="post" class="mt-4">
                        <input type="hidden" asp-for="Id" />
                        <button type="submit" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xác nhận xóa
                        </button>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại
                        </a>
                    </form>
                </div>
            </div>
        
</div> 