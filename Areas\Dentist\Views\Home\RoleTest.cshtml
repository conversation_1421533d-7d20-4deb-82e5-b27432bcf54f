@{
    ViewData["Title"] = "Kiểm tra phân quyền - Dentist";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-shield-alt me-2"></i>
                    Kiểm tra phân quyền - Dentist Panel
                </h1>
            </div>

            <div class="row">
                <!-- Current User Info -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-md me-2"></i>Thông tin bác sĩ hiện tại
                            </h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Tên đăng nhập:</strong> @(User.Identity?.Name ?? "Chưa đăng nhập")</p>
                            <p><strong>Đ<PERSON> xác thực:</strong> 
                                @if (User.Identity?.IsAuthenticated == true)
                                {
                                    <span class="badge bg-success">Có</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không</span>
                                }
                            </p>
                            <p><strong>Vai trò:</strong></p>
                            <ul class="list-unstyled">
                                @if (User.IsInRole("Admin"))
                                {
                                    <li><span class="badge bg-danger me-1">Admin</span> Quản trị viên</li>
                                }
                                @if (User.IsInRole("Staff"))
                                {
                                    <li><span class="badge bg-primary me-1">Staff</span> Nhân viên lễ tân</li>
                                }
                                @if (User.IsInRole("Dentist"))
                                {
                                    <li><span class="badge bg-success me-1">Dentist</span> Bác sĩ nha khoa</li>
                                }
                                @if (User.IsInRole("User"))
                                {
                                    <li><span class="badge bg-secondary me-1">User</span> Người dùng</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Dentist Permission Matrix -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user-md me-2"></i>Quyền hạn Dentist
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Chức năng</th>
                                            <th class="text-center">Quyền</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Xem lịch hẹn của mình</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Cập nhật trạng thái lịch hẹn</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Xem thông tin bệnh nhân</td>
                                            <td class="text-center">
                                                <i class="fas fa-eye text-warning" title="Chỉ xem"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Xem danh sách dịch vụ</td>
                                            <td class="text-center">
                                                <i class="fas fa-eye text-warning" title="Chỉ xem"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Tạo lịch hẹn mới</td>
                                            <td class="text-center">
                                                <i class="fas fa-times text-danger"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Quản lý thanh toán</td>
                                            <td class="text-center">
                                                <i class="fas fa-times text-danger"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Xem báo cáo doanh thu</td>
                                            <td class="text-center">
                                                <i class="fas fa-times text-danger"></i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Kiểm tra chức năng
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Calendar", "Appointment")" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-calendar-alt fa-2x mb-2"></i><br>
                                        Lịch hẹn của tôi
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Index", "Patient")" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-user-injured fa-2x mb-2"></i><br>
                                        Xem bệnh nhân
                                        <small class="d-block">Chỉ xem</small>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Index", "Service")" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-tooth fa-2x mb-2"></i><br>
                                        Xem dịch vụ
                                        <small class="d-block">Chỉ xem</small>
                                    </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Create", "Appointment", new { area = "Staff" })" class="btn btn-danger btn-lg w-100 disabled">
                                        <i class="fas fa-calendar-plus fa-2x mb-2"></i><br>
                                        Tạo lịch hẹn
                                        <small class="d-block">Không có quyền</small>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Index", "Payment", new { area = "Staff" })" class="btn btn-danger btn-lg w-100 disabled">
                                        <i class="fas fa-credit-card fa-2x mb-2"></i><br>
                                        Thanh toán
                                        <small class="d-block">Không có quyền</small>
                                    </a>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <a href="@Url.Action("Index", "Report", new { area = "Staff" })" class="btn btn-danger btn-lg w-100 disabled">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                        Báo cáo
                                        <small class="d-block">Không có quyền</small>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dentist Specific Information -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-success">
                        <h5 class="alert-heading">
                            <i class="fas fa-user-md me-2"></i>Thông tin Dentist Area
                        </h5>
                        <p class="mb-1">
                            <strong>Current Area:</strong> Dentist<br>
                            <strong>Layout:</strong> _DentistLayout.cshtml<br>
                            <strong>Sidebar:</strong> _DentistSidebar.cshtml<br>
                            <strong>Theme Color:</strong> Green (#388e3c)
                        </p>
                        <hr>
                        <p class="mb-0">
                            Bạn đang truy cập Dentist Area với quyền xem lịch hẹn của mình, cập nhật trạng thái điều trị, 
                            và xem thông tin bệnh nhân có liên quan đến lịch hẹn của bạn.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="row mt-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Chú thích:</h6>
                        <p class="mb-1">
                            <i class="fas fa-check text-success me-2"></i>Có quyền đầy đủ
                        </p>
                        <p class="mb-1">
                            <i class="fas fa-eye text-warning me-2"></i>Chỉ có quyền xem
                        </p>
                        <p class="mb-0">
                            <i class="fas fa-times text-danger me-2"></i>Không có quyền
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
