@model List<MyMvcApp.Models.Appointment>
@{
    ViewData["Title"] = "Lịch hẹn của tôi";
    Layout = "_UserLayout";
}

@section Styles {
    <style>
        .appointments-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .appointments-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            overflow: hidden;
        }
        
        .appointment-item {
            border-bottom: 1px solid #f8f9fa;
            padding: 20px;
            transition: background-color 0.3s ease;
        }
        
        .appointment-item:hover {
            background-color: #f8f9fa;
        }
        
        .appointment-item:last-child {
            border-bottom: none;
        }
        
        .appointment-date {
            font-size: 1.2rem;
            font-weight: 600;
            color: #667eea;
        }
        
        .appointment-service {
            font-size: 1.1rem;
            margin-bottom: 10px;
        }
        
        .appointment-details {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status-pending {
            background-color: #fff3cd;
            color: #856404;
        }
        
        .status-confirmed {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .status-completed {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status-cancelled {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .back-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
}

<div class="appointments-container">
    <div class="container">
        <div class="page-header">
            <h1><i class="fas fa-calendar-alt me-3"></i>Lịch hẹn của tôi</h1>
            <p class="mb-0">Quản lý và theo dõi các lịch hẹn của bạn</p>
        </div>
        
        <div class="appointments-card">
            @if (Model != null && Model.Count > 0)
            {
                @foreach (var appointment in Model)
                {
                    <div class="appointment-item">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <div class="appointment-date">
                                    <i class="fas fa-calendar me-2"></i>
                                    @appointment.AppointmentDate.ToString("dd/MM/yyyy")
                                </div>
                                <div class="appointment-details">
                                    <i class="fas fa-clock me-1"></i>
                                    @appointment.AppointmentDate.ToString("HH:mm")
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="appointment-service">
                                    <i class="fas fa-tooth me-2"></i>
                                    @(appointment.Service?.Name ?? "Dịch vụ không xác định")
                                </div>
                                <div class="appointment-details">
                                    <i class="fas fa-user-md me-1"></i>
                                    Bác sĩ: @(appointment.Dentist?.FullName ?? "Chưa phân công")
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="appointment-details">
                                    <i class="fas fa-money-bill-wave me-1"></i>
                                    @(appointment.Service?.Price.ToString("N0") ?? "0") VNĐ
                                </div>
                                @if (!string.IsNullOrEmpty(appointment.Notes))
                                {
                                    <div class="appointment-details mt-1">
                                        <i class="fas fa-sticky-note me-1"></i>
                                        @appointment.Notes
                                    </div>
                                }
                            </div>
                            <div class="col-md-2 text-end">
                                <span class="status-badge @GetStatusClass(appointment.Status)">
                                    @GetStatusText(appointment.Status)
                                </span>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="empty-state">
                    <i class="fas fa-calendar-times fa-4x mb-3"></i>
                    <h4>Chưa có lịch hẹn nào</h4>
                    <p>Bạn chưa có lịch hẹn nào trong hệ thống.</p>
                </div>
            }
        </div>
        
        <div class="text-center mt-4">
            <a href="@Url.Action("Index", "Home")" class="back-btn">
                <i class="fas fa-arrow-left me-2"></i>Về trang chủ
            </a>
        </div>
    </div>
</div>

@functions {
    private string GetStatusClass(string status)
    {
        return status?.ToLower() switch
        {
            "pending" => "status-pending",
            "confirmed" => "status-confirmed", 
            "completed" => "status-completed",
            "cancelled" => "status-cancelled",
            _ => "status-pending"
        };
    }
    
    private string GetStatusText(string status)
    {
        return status?.ToLower() switch
        {
            "pending" => "Chờ xác nhận",
            "confirmed" => "Đã xác nhận",
            "completed" => "Hoàn thành",
            "cancelled" => "Đã hủy",
            _ => "Chờ xác nhận"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add fade-in animation
            $('.appointment-item').each(function(index) {
                $(this).delay(index * 100).fadeIn(500);
            });
        });
    </script>
}
