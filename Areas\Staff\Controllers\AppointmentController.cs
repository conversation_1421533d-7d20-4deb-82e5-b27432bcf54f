using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.Data;
using System.Threading.Tasks;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using System.Linq;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;

namespace MyMvcApp.Areas.Staff.Controllers
{
    [Area("Staff")]
    [Authorize(Roles = "Admin,Staff")]
    public class AppointmentController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<AppointmentController> _logger;

        public AppointmentController(
            ApplicationDbContext context, 
            IUserService userService,
            UserManager<ApplicationUser> userManager,
            ILogger<AppointmentController> logger)
        {
            _context = context;
            _userService = userService;
            _userManager = userManager;
            _logger = logger;
        }

        public async Task<IActionResult> Index()
        {
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .OrderByDescending(a => a.AppointmentDate)
                .ThenBy(a => a.StartTime)
                .ToListAsync();

            return View(appointments);
        }

        public async Task<IActionResult> Create()
        {
            ViewBag.Patients = (await _context.Patients
                .Select(p => new { Id = p.Id, Name = p.FullName })
                .ToListAsync())
                .Select(p => new SelectListItem { Value = p.Id.ToString(), Text = p.Name })
                .ToList();

            ViewBag.Services = (await _context.Services
                .Where(s => s.IsActive)
                .Select(s => new { Id = s.Id, Name = s.Name })
                .ToListAsync())
                .Select(s => new SelectListItem { Value = s.Id.ToString(), Text = s.Name })
                .ToList();

            ViewBag.Dentists = (await _userManager.GetUsersInRoleAsync("Dentist"))
                .Select(d => new SelectListItem { Value = d.Id, Text = d.FullName })
                .ToList();

            return View();
        }

        public async Task<IActionResult> Calendar()
        {
            // Load ViewBag data for the modal form
            ViewBag.Patients = (await _context.Patients
                .Select(p => new { Id = p.Id, Name = p.FullName })
                .ToListAsync())
                .Select(p => new SelectListItem { Value = p.Id.ToString(), Text = p.Name })
                .ToList();

            ViewBag.Services = (await _context.Services
                .Where(s => s.IsActive)
                .Select(s => new { Id = s.Id, Name = s.Name })
                .ToListAsync())
                .Select(s => new SelectListItem { Value = s.Id.ToString(), Text = s.Name })
                .ToList();

            ViewBag.Dentists = (await _userManager.GetUsersInRoleAsync("Dentist"))
                .Select(d => new SelectListItem { Value = d.Id, Text = d.FullName })
                .ToList();

            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .OrderBy(a => a.AppointmentDate)
                .ThenBy(a => a.StartTime)
                .ToListAsync();

            return View(appointments);
        }

        [HttpGet]
        public async Task<IActionResult> GetAppointments()
        {
            var appointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.Status != "Cancelled")
                .ToListAsync();

            var result = appointments.Select(a => new
            {
                id = a.Id,
                title = $"{a.Patient?.FullName} - {a.Service?.Name}",
                start = a.AppointmentDate.ToString("yyyy-MM-dd") + "T" + a.StartTime.ToString(@"hh\:mm\:ss"),
                end = a.AppointmentDate.ToString("yyyy-MM-dd") + "T" + a.EndTime.ToString(@"hh\:mm\:ss"),
                backgroundColor = GetColorFromStatus(a.Status),
                borderColor = GetColorFromStatus(a.Status),
                extendedProps = new
                {
                    patientName = a.Patient?.FullName,
                    serviceName = a.Service?.Name,
                    dentistName = a.Dentist?.FullName,
                    status = a.Status,
                    notes = a.Notes
                }
            }).ToList();

            return Json(result);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(Appointment appointment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync(User);
                    if (currentUser != null)
                    {
                        appointment.CreatedBy = currentUser.Id;
                        appointment.CreatedByUser = currentUser;
                        appointment.CreatedAt = DateTime.Now;
                        appointment.Status = "Scheduled";

                        _context.Add(appointment);
                        await _context.SaveChangesAsync();

                        // Log activity
                        var activity = new Activity
                        {
                            Time = DateTime.Now,
                            Description = $"Tạo lịch hẹn mới cho bệnh nhân ID: {appointment.PatientId}",
                            UserId = currentUser.Id,
                            User = currentUser
                        };
                        _context.Activities.Add(activity);
                        await _context.SaveChangesAsync();

                        TempData["SuccessMessage"] = "Tạo lịch hẹn thành công.";
                        return RedirectToAction(nameof(Index));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating appointment");
                    ModelState.AddModelError("", "Có lỗi xảy ra khi tạo lịch hẹn. Vui lòng thử lại.");
                }
            }

            // Reload ViewBag data if validation fails
            ViewBag.Patients = (await _context.Patients
                .Select(p => new { Id = p.Id, Name = p.FullName })
                .ToListAsync())
                .Select(p => new SelectListItem { Value = p.Id.ToString(), Text = p.Name })
                .ToList();

            ViewBag.Services = (await _context.Services
                .Where(s => s.IsActive)
                .Select(s => new { Id = s.Id, Name = s.Name })
                .ToListAsync())
                .Select(s => new SelectListItem { Value = s.Id.ToString(), Text = s.Name })
                .ToList();

            ViewBag.Dentists = (await _userManager.GetUsersInRoleAsync("Dentist"))
                .Select(d => new SelectListItem { Value = d.Id, Text = d.FullName })
                .ToList();

            return View(appointment);
        }

        private string GetColorFromStatus(string status)
        {
            return status switch
            {
                "Completed" => "#28a745",
                "Cancelled" => "#dc3545",
                "No-show" => "#6c757d",
                _ => "#007bff"
            };
        }
    }
}
