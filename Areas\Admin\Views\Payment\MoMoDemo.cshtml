@{
    ViewData["Title"] = "Demo MoMo Payment";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Demo MoMo Payment Integration</h1>
            </div>

            
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-mobile-alt"></i> MoMo Payment Demo</h5>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Thông tin Demo:</h6>
                                <ul>
                                    <li>Đây là môi trường test/sandbox của MoMo</li>
                                    <li>Không có giao dịch thật đư<PERSON><PERSON> thực hiện</li>
                                    <li>QR Code được tạo tự động cho mỗi giao dịch</li>
                                    <li>Có thể test cả thanh toán tiền mặt và MoMo</li>
                                </ul>
                            </div>

                            <form id="demoPaymentForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>Tên khách hàng:</label>
                                            <input type="text" class="form-control" id="customerName" value="Nguyễn Văn A" readonly>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>Dịch vụ:</label>
                                            <input type="text" class="form-control" id="serviceName" value="Trám răng" readonly>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>Số tiền:</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="amount" value="150000" min="1000" max="50000000">
                                                <span class="input-group-text">VNĐ</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label>Mô tả:</label>
                                            <input type="text" class="form-control" id="orderInfo" value="Thanh toán dịch vụ nha khoa">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-primary btn-lg w-100" onclick="createMoMoDemo()">
                                            <i class="fas fa-qrcode"></i> Tạo QR MoMo Demo
                                        </button>
                                    </div>
                                    <div class="col-md-6">
                                        <button type="button" class="btn btn-success btn-lg w-100" onclick="simulateCashPayment()">
                                            <i class="fas fa-money-bill-wave"></i> Mô phỏng thanh toán tiền mặt
                                        </button>
                                    </div>
                                </div>

                                <div class="row mt-3">
                                    <div class="col-md-12">
                                        <button type="button" class="btn btn-warning btn-sm" onclick="testQRSimple()">
                                            <i class="fas fa-bug"></i> Test QR Simple
                                        </button>
                                        <button type="button" class="btn btn-info btn-sm" onclick="debugQRLibraries()">
                                            <i class="fas fa-info"></i> Debug Libraries
                                        </button>
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="testModal()">
                                            <i class="fas fa-window-maximize"></i> Test Modal
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0"><i class="fas fa-cogs"></i> Cấu hình MoMo</h6>
                        </div>
                        <div class="card-body">
                            <small>
                                <strong>Partner Code:</strong> MOMO<br>
                                <strong>Environment:</strong> Test<br>
                                <strong>Endpoint:</strong> test-payment.momo.vn<br>
                                <strong>Request Type:</strong> captureWallet<br>
                                <strong>Language:</strong> vi
                            </small>
                        </div>
                    </div>

                    <div class="card mt-3">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0"><i class="fas fa-list"></i> Lịch sử Demo</h6>
                        </div>
                        <div class="card-body">
                            <div id="demoHistory">
                                <p class="text-muted">Chưa có giao dịch demo nào</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Kết quả Demo -->
            <div class="row mt-4" id="demoResult" style="display: none;">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0"><i class="fas fa-receipt"></i> Kết quả Demo</h5>
                        </div>
                        <div class="card-body">
                            <div id="demoContent">
                                <!-- Nội dung demo sẽ được hiển thị ở đây -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        

<!-- Modal QR Code -->
<div class="modal fade" id="qrDemoModal" tabindex="-1" aria-labelledby="qrDemoModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="qrDemoModalLabel">
                    <i class="fas fa-qrcode"></i> Demo QR Code MoMo
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div id="qrDemoContainer">
                    <!-- QR Code demo sẽ được hiển thị ở đây -->
                </div>
                <div class="alert alert-warning mt-3">
                    <strong>Lưu ý:</strong> Đây chỉ là demo. QR Code này không thể sử dụng để thanh toán thật.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-success" onclick="simulatePaymentSuccess()">
                    <i class="fas fa-check"></i> Mô phỏng thanh toán thành công
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        let demoTransactions = [];

        function createMoMoDemo() {
            const amount = document.getElementById('amount').value;
            const orderInfo = document.getElementById('orderInfo').value;
            const customerName = document.getElementById('customerName').value;

            if (!amount || amount < 1000) {
                alert('Số tiền phải ít nhất 1,000 VNĐ');
                return;
            }

            // Tạo dữ liệu demo
            const orderId = 'DEMO_' + Date.now();
            const demoData = {
                orderId: orderId,
                amount: amount,
                orderInfo: orderInfo,
                customerName: customerName,
                timestamp: new Date().toLocaleString('vi-VN'),
                status: 'Pending'
            };

            // Tạo QR Code demo
            const qrData = `DEMO_MOMO:${orderId}:${amount}:${orderInfo}`;

            document.getElementById('qrDemoContainer').innerHTML = `
                <div id="qrcode-demo"></div>
                <p class="mt-3"><strong>Mã đơn hàng:</strong> ${orderId}</p>
                <p><strong>Số tiền:</strong> ${parseInt(amount).toLocaleString('vi-VN')} VNĐ</p>
                <p><strong>Mô tả:</strong> ${orderInfo}</p>
            `;

            // Tạo QR code với fallback
            try {
                if (typeof QRCode !== 'undefined') {
                    QRCode.toCanvas(document.getElementById('qrcode-demo'), qrData, {
                        width: 256,
                        height: 256
                    }, function (error) {
                        if (error) {
                            console.error('QR Code error:', error);
                            generateQRFallback(qrData, orderId, amount, orderInfo);
                        }
                    });
                } else {
                    generateQRFallback(qrData, orderId, amount, orderInfo);
                }
            } catch (e) {
                console.error('QR Code exception:', e);
                generateQRFallback(qrData, orderId, amount, orderInfo);
            }

            // Lưu vào lịch sử demo
            demoTransactions.push(demoData);
            updateDemoHistory();

            // Hiển thị modal với Bootstrap 5
            const modal = new bootstrap.Modal(document.getElementById('qrDemoModal'));
            modal.show();
        }

        function simulateCashPayment() {
            const amount = document.getElementById('amount').value;
            const orderInfo = document.getElementById('orderInfo').value;
            const customerName = document.getElementById('customerName').value;

            if (!amount || amount < 1000) {
                alert('Số tiền phải ít nhất 1,000 VNĐ');
                return;
            }

            const orderId = 'CASH_' + Date.now();
            const demoData = {
                orderId: orderId,
                amount: amount,
                orderInfo: orderInfo,
                customerName: customerName,
                timestamp: new Date().toLocaleString('vi-VN'),
                status: 'Success',
                paymentMethod: 'Cash'
            };

            demoTransactions.push(demoData);
            updateDemoHistory();

            // Hiển thị kết quả
            showDemoResult(demoData);

            alert('Mô phỏng thanh toán tiền mặt thành công!');
        }

        function simulatePaymentSuccess() {
            if (demoTransactions.length > 0) {
                const lastTransaction = demoTransactions[demoTransactions.length - 1];
                lastTransaction.status = 'Success';
                lastTransaction.paymentMethod = 'MoMo';

                updateDemoHistory();
                showDemoResult(lastTransaction);

                const modal = bootstrap.Modal.getInstance(document.getElementById('qrDemoModal'));
                modal.hide();
                alert('Mô phỏng thanh toán MoMo thành công!');
            }
        }

        function updateDemoHistory() {
            const historyContainer = document.getElementById('demoHistory');

            if (demoTransactions.length === 0) {
                historyContainer.innerHTML = '<p class="text-muted">Chưa có giao dịch demo nào</p>';
                return;
            }

            let historyHtml = '';
            demoTransactions.slice(-5).reverse().forEach(transaction => {
                const statusClass = transaction.status === 'Success' ? 'success' : 'warning';
                historyHtml += `
                    <div class="border-bottom pb-2 mb-2">
                        <small>
                            <strong>${transaction.orderId}</strong><br>
                            ${parseInt(transaction.amount).toLocaleString('vi-VN')} VNĐ<br>
                            <span class="badge bg-${statusClass}">${transaction.status}</span><br>
                            <span class="text-muted">${transaction.timestamp}</span>
                        </small>
                    </div>
                `;
            });

            historyContainer.innerHTML = historyHtml;
        }

        function showDemoResult(transaction) {
            const resultContainer = document.getElementById('demoContent');

            resultContainer.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>Thông tin giao dịch:</h6>
                        <ul class="list-unstyled">
                            <li><strong>Mã đơn hàng:</strong> ${transaction.orderId}</li>
                            <li><strong>Khách hàng:</strong> ${transaction.customerName}</li>
                            <li><strong>Số tiền:</strong> ${parseInt(transaction.amount).toLocaleString('vi-VN')} VNĐ</li>
                            <li><strong>Phương thức:</strong> ${transaction.paymentMethod || 'MoMo'}</li>
                            <li><strong>Trạng thái:</strong> <span class="badge bg-success">${transaction.status}</span></li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Hóa đơn demo:</h6>
                        <div class="border p-3">
                            <div class="text-center">
                                <h6>HÓA ĐƠN DEMO</h6>
                                <p>Phòng khám nha khoa</p>
                                <hr>
                                <p><strong>Dịch vụ:</strong> ${transaction.orderInfo}</p>
                                <p><strong>Tổng tiền:</strong> ${parseInt(transaction.amount).toLocaleString('vi-VN')} VNĐ</p>
                                <p><strong>Ngày:</strong> ${transaction.timestamp}</p>
                                <hr>
                                <small class="text-muted">Đây là hóa đơn demo</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.getElementById('demoResult').style.display = 'block';
        }

        function generateQRFallback(qrData, orderId, amount, orderInfo) {
            console.log('Using QR Server API fallback...');

            // Sử dụng QR Server API
            const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?size=256x256&data=${encodeURIComponent(qrData)}`;

            document.getElementById('qrcode-demo').innerHTML = `
                <img src="${qrServerUrl}" alt="QR Code" class="img-fluid" style="max-width: 256px;"
                     onload="console.log('QR Server API successful')"
                     onerror="showQRError()">
                <p class="text-info mt-2"><small>📡 Generated using QR Server API</small></p>
            `;
        }

        function showQRError() {
            document.getElementById('qrcode-demo').innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> Không thể tạo QR Code</h6>
                    <p>Có lỗi xảy ra khi tạo mã QR. Vui lòng thử lại sau.</p>
                    <button class="btn btn-primary btn-sm" onclick="location.reload()">
                        <i class="fas fa-redo"></i> Thử lại
                    </button>
                </div>
            `;
        }

        // Debug function
        function debugQRLibraries() {
            console.log('QRCode library available:', typeof QRCode !== 'undefined');
            console.log('Bootstrap available:', typeof bootstrap !== 'undefined');

            if (typeof QRCode === 'undefined') {
                console.error('QRCode library not loaded!');
            }
            if (typeof bootstrap === 'undefined') {
                console.error('Bootstrap library not loaded!');
            }
        }

        function testQRSimple() {
            console.log('Testing simple QR generation...');

            const testData = 'Test QR Code - Simple';
            const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(testData)}`;

            // Tạo modal test
            const testModal = document.createElement('div');
            testModal.innerHTML = `
                <div class="modal fade" id="testModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">Test QR Code</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body text-center">
                                <img src="${qrServerUrl}" alt="Test QR" class="img-fluid">
                                <p class="mt-2">Test QR Code: ${testData}</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(testModal);
            const modal = new bootstrap.Modal(document.getElementById('testModal'));
            modal.show();
        }

        function testModal() {
            console.log('Testing modal functionality...');

            // Test modal cơ bản
            const modal = new bootstrap.Modal(document.getElementById('qrDemoModal'));

            // Thêm test content
            document.getElementById('qrcode-demo').innerHTML = `
                <div class="alert alert-info">
                    <h6>Modal Test</h6>
                    <p>Modal đang hoạt động bình thường!</p>
                    <p>Bootstrap version: ${bootstrap.Tooltip.VERSION || 'Unknown'}</p>
                </div>
            `;

            document.getElementById('qr-info-demo').innerHTML = `
                <p><strong>Test Modal:</strong> SUCCESS</p>
                <p><strong>Time:</strong> ${new Date().toLocaleString()}</p>
            `;

            modal.show();
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MoMo Demo page loaded');
            debugQRLibraries();

            // Test các thư viện cần thiết
            setTimeout(() => {
                console.log('Page fully loaded, running tests...');
                if (typeof QRCode === 'undefined') {
                    console.warn('QRCode library not available, will use fallback');
                }
                if (typeof bootstrap === 'undefined') {
                    console.error('Bootstrap not available!');
                }
            }, 1000);
        });
    </script>
}
