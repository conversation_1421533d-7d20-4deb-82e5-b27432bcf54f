    /*
    Flaticon icon font: Flaticon
    Creation date: 20/09/2018 05:43
    */

    @font-face {
  font-family: "Flaticon";
  src: url("./Flaticon.eot");
  src: url("./Flaticon.eot?#iefix") format("embedded-opentype"),
       url("./Flaticon.woff") format("woff"),
       url("./Flaticon.ttf") format("truetype"),
       url("./Flaticon.svg#Flaticon") format("svg");
  font-weight: normal;
  font-style: normal;
}

@media screen and (-webkit-min-device-pixel-ratio:0) {
  @font-face {
    font-family: "Flaticon";
    src: url("./Flaticon.svg#Flaticon") format("svg");
  }
}

    .fi:before{
        display: inline-block;
  font-family: "Flaticon";
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  line-height: 1;
  text-decoration: inherit;
  text-rendering: optimizeLegibility;
  text-transform: none;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-smoothing: antialiased;
    }

    .flaticon-tooth-1:before { content: "\f100"; }
.flaticon-anesthesia:before { content: "\f101"; }
.flaticon-tooth-with-braces:before { content: "\f102"; }
.flaticon-dentist:before { content: "\f103"; }
.flaticon-tooth:before { content: "\f104"; }
.flaticon-dental-care-1:before { content: "\f105"; }
.flaticon-dental-care:before { content: "\f106"; }
.flaticon-bacteria:before { content: "\f107"; }
    
    $font-Flaticon-tooth-1: "\f100";
    $font-Flaticon-anesthesia: "\f101";
    $font-Flaticon-tooth-with-braces: "\f102";
    $font-Flaticon-dentist: "\f103";
    $font-Flaticon-tooth: "\f104";
    $font-Flaticon-dental-care-1: "\f105";
    $font-Flaticon-dental-care: "\f106";
    $font-Flaticon-bacteria: "\f107";