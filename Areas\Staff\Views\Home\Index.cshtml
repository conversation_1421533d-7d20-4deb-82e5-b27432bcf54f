@{
    ViewData["Title"] = "Staff Dashboard";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard - Nhân viên lễ tân
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="@Url.Action("Calendar", "Appointment")" class="btn btn-primary">
                            <i class="fas fa-calendar-plus me-1"></i>Tạo lịch hẹn
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Tổng số bệnh nhân
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalPatients</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-injured fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Lịch hẹn hôm nay
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TodayAppointments</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Tổng dịch vụ
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.TotalServices</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tooth fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Thanh toán chờ xử lý
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.PendingPayments</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Thao tác nhanh
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Create", "Appointment")" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-calendar-plus fa-2x mb-2"></i><br>
                                Tạo lịch hẹn mới
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Create", "Patient")" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-user-plus fa-2x mb-2"></i><br>
                                Thêm bệnh nhân
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Payment")" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i><br>
                                Quản lý thanh toán
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="@Url.Action("Index", "Report")" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-chart-line fa-2x mb-2"></i><br>
                                Xem báo cáo
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activities -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>Hoạt động gần đây
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.RecentActivities != null && ((IEnumerable<dynamic>)ViewBag.RecentActivities).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Thời gian</th>
                                        <th>Mô tả</th>
                                        <th>Người thực hiện</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var activity in (IEnumerable<dynamic>)ViewBag.RecentActivities)
                                    {
                                        <tr>
                                            <td>@activity.Time.ToString("dd/MM/yyyy HH:mm")</td>
                                            <td>@activity.Description</td>
                                            <td>@activity.User</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Chưa có hoạt động nào được ghi nhận</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
</style>
