@model MyMvcApp.Models.ApplicationUser
@{
    ViewData["Title"] = "Thông tin cá nhân";
    Layout = "_UserLayout";
}

@section Styles {
    <style>
        .profile-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }
        
        .profile-body {
            padding: 40px;
        }
        
        .info-item {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            border-left: 4px solid #667eea;
        }
        
        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }
        
        .info-value {
            color: #212529;
            font-size: 1.1rem;
        }
        
        .action-buttons {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            color: white;
        }
    </style>
}

<div class="profile-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <h2 class="mb-2">@Model.FullName</h2>
                        <p class="mb-0 opacity-75">Thành viên của hệ thống</p>
                    </div>
                    
                    <div class="profile-body">
                        <h4 class="mb-4">
                            <i class="fas fa-info-circle me-2"></i>Thông tin cá nhân
                        </h4>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-user me-2"></i>Họ và tên
                            </div>
                            <div class="info-value">@Model.FullName</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-envelope me-2"></i>Email
                            </div>
                            <div class="info-value">@Model.Email</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-phone me-2"></i>Số điện thoại
                            </div>
                            <div class="info-value">@(Model.PhoneNumber ?? "Chưa cập nhật")</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar me-2"></i>Ngày sinh
                            </div>
                            <div class="info-value">@Model.DateOfBirth.ToString("dd/MM/yyyy")</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-venus-mars me-2"></i>Giới tính
                            </div>
                            <div class="info-value">@Model.Gender</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-clock me-2"></i>Ngày tham gia
                            </div>
                            <div class="info-value">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</div>
                        </div>
                        
                        @if (!string.IsNullOrEmpty(Model.Address))
                        {
                            <div class="info-item">
                                <div class="info-label">
                                    <i class="fas fa-map-marker-alt me-2"></i>Địa chỉ
                                </div>
                                <div class="info-value">@Model.Address</div>
                            </div>
                        }
                    </div>
                    
                    <div class="action-buttons">
                        <a href="@Url.Action("UpdateProfile", "Home")" class="btn-custom">
                            <i class="fas fa-edit me-2"></i>Chỉnh sửa thông tin
                        </a>
                        <a href="@Url.Action("ChangePassword", "Home")" class="btn-custom">
                            <i class="fas fa-key me-2"></i>Đổi mật khẩu
                        </a>
                        @if (User.IsInRole("Admin"))
                        {
                            <a href="@Url.Action("Index", "Home", new { area = "Admin" })" class="btn-custom">
                                <i class="fas fa-tachometer-alt me-2"></i>Admin Dashboard
                            </a>
                        }
                        <a href="@Url.Action("Index", "Home")" class="btn-custom">
                            <i class="fas fa-home me-2"></i>Về trang chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            // Add fade-in animation
            $('.info-item').each(function(index) {
                $(this).delay(index * 100).fadeIn(500);
            });
        });
    </script>
}
