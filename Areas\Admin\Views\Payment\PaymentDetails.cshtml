@model MyMvcApp.Models.Appointment

@{
    ViewData["Title"] = "Chi tiết thanh toán";
    var existingTransaction = ViewBag.ExistingTransaction as MyMvcApp.Models.PaymentTransaction;
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chi tiết thanh toán</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <a asp-action="Index" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Quay lại danh sách
                    </a>
                </div>
            </div>

            <!-- Thông tin lịch hẹn -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông tin lịch hẹn #@Model.Id</h5>
                </div>
                <div class="card-body">
                    
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Bệnh nhân:</dt>
                                <dd class="col-sm-8">@Model.Patient.FullName</dd>

                                <dt class="col-sm-4">Email:</dt>
                                <dd class="col-sm-8">@Model.Patient.Email</dd>

                                <dt class="col-sm-4">Số điện thoại:</dt>
                                <dd class="col-sm-8">@Model.Patient.PhoneNumber</dd>

                                <dt class="col-sm-4">Dịch vụ:</dt>
                                <dd class="col-sm-8">@Model.Service.Name</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="row">
                                <dt class="col-sm-4">Bác sĩ:</dt>
                                <dd class="col-sm-8">@Model.Dentist.FullName</dd>

                                <dt class="col-sm-4">Ngày khám:</dt>
                                <dd class="col-sm-8">@Model.AppointmentDate.ToString("dd/MM/yyyy")</dd>

                                <dt class="col-sm-4">Giờ khám:</dt>
                                <dd class="col-sm-8">@Model.StartTime.ToString(@"hh\:mm") - @Model.EndTime.ToString(@"hh\:mm")</dd>

                                <dt class="col-sm-4">Số tiền:</dt>
                                <dd class="col-sm-8">
                                    <span class="fw-bold text-success fs-5">@Model.Service.Price.ToString("N0") VNĐ</span>
                                </dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            @if (existingTransaction != null)
            {
                <!-- Thông tin thanh toán đã có -->
                <div class="card mb-4">
                    <div class="card-header @(existingTransaction.Status == "Success" ? "bg-success" : existingTransaction.Status == "Pending" ? "bg-warning" : "bg-danger") text-white">
                        <h5 class="mb-0">Trạng thái thanh toán</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">Mã giao dịch:</dt>
                                    <dd class="col-sm-8">@existingTransaction.OrderId</dd>

                                    <dt class="col-sm-4">Phương thức:</dt>
                                    <dd class="col-sm-8">@existingTransaction.PaymentMethod</dd>

                                    <dt class="col-sm-4">Trạng thái:</dt>
                                    <dd class="col-sm-8">
                                        <span class="badge @(existingTransaction.Status == "Success" ? "bg-success" : existingTransaction.Status == "Pending" ? "bg-warning" : "bg-danger")">
                                            @(existingTransaction.Status == "Success" ? "Đã thanh toán" : existingTransaction.Status == "Pending" ? "Chờ thanh toán" : "Thất bại")
                                        </span>
                                    </dd>
                                </dl>
                            </div>
                            <div class="col-md-6">
                                <dl class="row">
                                    <dt class="col-sm-4">Ngày tạo:</dt>
                                    <dd class="col-sm-8">@existingTransaction.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>

                                    @if (existingTransaction.CompletedAt.HasValue)
                                    {
                                        <dt class="col-sm-4">Ngày hoàn thành:</dt>
                                        <dd class="col-sm-8">@existingTransaction.CompletedAt.Value.ToString("dd/MM/yyyy HH:mm")</dd>
                                    }
                                </dl>
                            </div>
                        </div>

                        @if (existingTransaction.Status == "Success")
                        {
                            <div class="mt-3">
                                <a asp-action="GenerateInvoice" asp-route-appointmentId="@Model.Id" class="btn btn-success" target="_blank">
                                    <i class="fas fa-file-invoice"></i> Xuất hóa đơn
                                </a>
                            </div>
                        }
                    </div>
                </div>
            }
            else
            {
                <!-- Form thanh toán -->
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Chọn phương thức thanh toán</h5>
                    </div>
                    <div class="card-body">
                        @Html.AntiForgeryToken()
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-mobile-alt fa-3x text-primary mb-3"></i>
                                        <h5>Thanh toán MoMo</h5>
                                        <p class="text-muted">Quét mã QR để thanh toán qua ví MoMo</p>
                                        <button type="button" class="btn btn-primary" onclick="createMoMoPayment(@Model.Id)">
                                            <i class="fas fa-qrcode"></i> Tạo mã QR MoMo
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-money-bill-wave fa-3x text-success mb-3"></i>
                                        <h5>Thanh toán tiền mặt</h5>
                                        <p class="text-muted">Thanh toán trực tiếp bằng tiền mặt</p>
                                        <button type="button" class="btn btn-success" onclick="createCashPayment(@Model.Id)">
                                            <i class="fas fa-hand-holding-usd"></i> Thanh toán tiền mặt
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        

<!-- Modal hiển thị QR Code -->
<div class="modal fade" id="qrModal" tabindex="-1" aria-labelledby="qrModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="qrModalLabel">Quét mã QR để thanh toán</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <div id="qrCodeContainer">
                    <!-- QR Code sẽ được hiển thị ở đây -->
                </div>
                <p class="mt-3 text-muted">Sử dụng ứng dụng MoMo để quét mã QR và thanh toán</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-info" onclick="refreshQRCode()">
                    <i class="fas fa-redo"></i> Làm mới QR
                </button>
                <button type="button" class="btn btn-primary" onclick="checkPaymentStatus()">
                    <i class="fas fa-search"></i> Kiểm tra thanh toán
                </button>
                <button type="button" class="btn btn-success" onclick="simulatePaymentSuccess()" title="Chỉ dùng để test">
                    <i class="fas fa-check"></i> Mô phỏng thành công
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let currentOrderId = '';
        let qrRetryCount = 0;
        const MAX_QR_RETRY = 3;
        let qrSuccessShown = false;
        let qrTimeoutId = null;
        const QR_TIMEOUT_MINUTES = 5;

        function createMoMoPayment(appointmentId) {
            // Sử dụng fetch API thay vì jQuery
            const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

            fetch('@Url.Action("CreateMoMoPayment")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `appointmentId=${appointmentId}&__RequestVerificationToken=${encodeURIComponent(token)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentOrderId = data.orderId;

                    // Lưu thông tin payment để sử dụng trong các function khác
                    window.currentPaymentData = {
                        orderId: data.orderId,
                        amount: data.amount,
                        patientName: data.patientName,
                        serviceName: data.serviceName,
                        payUrl: data.payUrl,
                        qrCodeUrl: data.qrCodeUrl
                    };

                    // Hiển thị QR Code với fallback strategy
                    displayQRCode(data.qrCodeUrl, data.payUrl, data.orderId);

                    // Sử dụng Bootstrap 5 modal
                    const modal = new bootstrap.Modal(document.getElementById('qrModal'));
                    modal.show();
                } else {
                    alert('Lỗi: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi tạo thanh toán MoMo');
            });
        }

        function createCashPayment(appointmentId) {
            if (confirm('Xác nhận đã nhận thanh toán tiền mặt?')) {
                const token = document.querySelector('input[name="__RequestVerificationToken"]').value;

                fetch('@Url.Action("CreateCashPayment")', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `appointmentId=${appointmentId}&__RequestVerificationToken=${encodeURIComponent(token)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Thanh toán tiền mặt thành công!');
                        location.reload();
                    } else {
                        alert('Lỗi: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra khi tạo thanh toán tiền mặt');
                });
            }
        }

        function displayQRCode(qrCodeUrl, payUrl, orderId) {
            const container = document.getElementById('qrCodeContainer');

            // Reset các flag khi tạo QR mới
            qrRetryCount = 0;
            qrSuccessShown = false;

            // Clear timeout cũ nếu có
            if (qrTimeoutId) {
                clearTimeout(qrTimeoutId);
                qrTimeoutId = null;
            }

            // Hiển thị loading
            container.innerHTML = `
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Đang tạo QR Code...</span>
                    </div>
                    <p class="mt-2">Đang tạo mã QR...</p>
                </div>
            `;

            if (qrCodeUrl) {
                // Sử dụng QR code URL từ server
                container.innerHTML = `
                    <div class="text-center">
                        <img src="${qrCodeUrl}" alt="QR Code" class="img-fluid border rounded"
                             style="max-width: 300px;"
                             onload="showQRSuccess('${orderId}')"
                             onerror="handleQRError('${payUrl}', '${orderId}')">
                    </div>
                `;
            } else {
                // Fallback ngay lập tức
                generateQRFallback(payUrl, orderId);
            }
        }

        // Xử lý lỗi QR Code với retry logic
        function handleQRError(payUrl, orderId) {
            qrRetryCount++;
            console.log(`QR Error - Retry count: ${qrRetryCount}`);

            if (qrRetryCount <= MAX_QR_RETRY) {
                // Thử fallback QR server
                generateQRFallback(payUrl, orderId);
            } else {
                // Đã thử quá nhiều lần, hiển thị lỗi cuối cùng
                showQRError();
            }
        }

        function generateQRFallback(payUrl, orderId) {
            const container = document.getElementById('qrCodeContainer');

            // Sử dụng QR Server API làm fallback
            const qrServerUrl = `https://api.qrserver.com/v1/create-qr-code/?size=300x300&data=${encodeURIComponent(payUrl)}`;

            container.innerHTML = `
                <div class="text-center">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle"></i> Đang tải mã QR từ server dự phòng...
                    </div>
                    <img src="${qrServerUrl}" alt="QR Code" class="img-fluid border rounded"
                         style="max-width: 300px;"
                         onload="showQRSuccess('${orderId}')"
                         onerror="handleQRError('${payUrl}', '${orderId}')">
                </div>
            `;
        }

        function showQRSuccess(orderId) {
            // Tránh hiển thị nhiều lần
            if (qrSuccessShown) {
                return;
            }
            qrSuccessShown = true;

            const container = document.getElementById('qrCodeContainer');
            const existingImg = container.querySelector('img');

            // Chỉ giữ lại QR image và thay thế toàn bộ nội dung
            container.innerHTML = `
                <div class="text-center">
                    ${existingImg ? existingImg.outerHTML : ''}
                    <div class="mt-3">
                        <div class="alert alert-success">
                            <h6><i class="fas fa-qrcode"></i> Mã QR đã sẵn sàng!</h6>
                            <p class="mb-2"><strong>Mã đơn hàng:</strong> ${orderId}</p>
                            <p class="mb-2"><strong>Bệnh nhân:</strong> ${window.currentPaymentData?.patientName || '@Model.Patient.FullName'}</p>
                            <p class="mb-0"><strong>Số tiền:</strong> ${window.currentPaymentData?.amount ? window.currentPaymentData.amount.toLocaleString('vi-VN') : '@Model.Service.Price.ToString("N0")'} VNĐ</p>
                        </div>
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-clock"></i> Thời gian hiệu lực:</h6>
                            <p class="mb-2">Mã QR có hiệu lực trong <strong>${QR_TIMEOUT_MINUTES} phút</strong></p>
                            <div id="qrCountdown" class="fw-bold text-danger"></div>
                        </div>
                        <div class="alert alert-info">
                            <h6><i class="fas fa-mobile-alt"></i> Hướng dẫn thanh toán:</h6>
                            <ol class="mb-0">
                                <li>Mở ứng dụng MoMo trên điện thoại</li>
                                <li>Chọn "Quét mã QR"</li>
                                <li>Quét mã QR hiển thị trên màn hình</li>
                                <li>Xác nhận thông tin và thanh toán</li>
                                <li>Chờ thông báo thanh toán thành công</li>
                            </ol>
                        </div>
                        <button class="btn btn-primary me-2" onclick="checkPaymentStatus()">
                            <i class="fas fa-search"></i> Kiểm tra thanh toán
                        </button>
                        <button class="btn btn-secondary me-2" onclick="refreshQRCode()">
                            <i class="fas fa-refresh"></i> Làm mới QR
                        </button>
                        <button class="btn btn-success" onclick="simulatePaymentSuccess()">
                            <i class="fas fa-check"></i> Mô phỏng thành công
                        </button>
                    </div>
                </div>
            `;

            // Bắt đầu đếm ngược
            startQRCountdown();
        }

        function showQRError() {
            const container = document.getElementById('qrCodeContainer');
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <h6><i class="fas fa-exclamation-triangle"></i> Không thể tạo mã QR</h6>
                    <p class="mb-3">Có lỗi xảy ra khi tạo mã QR sau ${MAX_QR_RETRY} lần thử.</p>
                    <div class="alert alert-info">
                        <h6><i class="fas fa-lightbulb"></i> Giải pháp thay thế:</h6>
                        <p class="mb-2">1. Kiểm tra kết nối internet và thử lại</p>
                        <p class="mb-2">2. Sử dụng thanh toán tiền mặt</p>
                        <p class="mb-0">3. Liên hệ bộ phận kỹ thuật nếu vấn đề tiếp tục</p>
                    </div>
                    <button class="btn btn-secondary me-2" onclick="location.reload()">
                        <i class="fas fa-refresh"></i> Tải lại trang
                    </button>
                    <button class="btn btn-warning" onclick="bootstrap.Modal.getInstance(document.getElementById('qrModal')).hide()">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            `;
        }

        // Bắt đầu đếm ngược QR timeout
        function startQRCountdown() {
            let timeLeft = QR_TIMEOUT_MINUTES * 60; // Convert to seconds

            function updateCountdown() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                const countdownElement = document.getElementById('qrCountdown');

                if (countdownElement) {
                    if (timeLeft > 0) {
                        countdownElement.innerHTML = `Còn lại: ${minutes}:${seconds.toString().padStart(2, '0')}`;
                        timeLeft--;
                    } else {
                        countdownElement.innerHTML = 'Mã QR đã hết hạn!';
                        showQRExpired();
                        return;
                    }
                }
            }

            // Update immediately
            updateCountdown();

            // Set timeout để hết hạn QR
            qrTimeoutId = setInterval(updateCountdown, 1000);
        }

        // Hiển thị QR hết hạn
        function showQRExpired() {
            if (qrTimeoutId) {
                clearInterval(qrTimeoutId);
                qrTimeoutId = null;
            }

            const container = document.getElementById('qrCodeContainer');
            container.innerHTML = `
                <div class="alert alert-danger text-center">
                    <h6><i class="fas fa-clock"></i> Mã QR đã hết hạn</h6>
                    <p class="mb-3">Mã QR đã hết hiệu lực sau ${QR_TIMEOUT_MINUTES} phút.</p>
                    <button class="btn btn-primary me-2" onclick="refreshQRCode()">
                        <i class="fas fa-refresh"></i> Tạo mã QR mới
                    </button>
                    <button class="btn btn-secondary" onclick="bootstrap.Modal.getInstance(document.getElementById('qrModal')).hide()">
                        <i class="fas fa-times"></i> Đóng
                    </button>
                </div>
            `;
        }

        function refreshQRCode() {
            if (window.currentPaymentData) {
                // Reset các flag và timeout
                qrRetryCount = 0;
                qrSuccessShown = false;
                if (qrTimeoutId) {
                    clearInterval(qrTimeoutId);
                    qrTimeoutId = null;
                }
                displayQRCode(window.currentPaymentData.qrCodeUrl, window.currentPaymentData.payUrl, window.currentPaymentData.orderId);
            } else {
                alert('Không có thông tin thanh toán để làm mới');
            }
        }

        function checkPaymentStatus() {
            if (!currentOrderId) {
                alert('Không có giao dịch nào để kiểm tra');
                return;
            }

            // Hiển thị loading trong một modal riêng
            const statusModal = document.createElement('div');
            statusModal.className = 'modal fade';
            statusModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Kiểm tra thanh toán</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Đang kiểm tra...</span>
                            </div>
                            <p>Đang kiểm tra trạng thái thanh toán...</p>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(statusModal);
            const modal = new bootstrap.Modal(statusModal);
            modal.show();

            setTimeout(() => {
                statusModal.querySelector('.modal-body').innerHTML = `
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-info-circle"></i> Trạng thái thanh toán</h6>
                        <p class="mb-3">Chưa nhận được xác nhận thanh toán từ MoMo.</p>
                        <button class="btn btn-success me-2" onclick="simulatePaymentSuccess(); bootstrap.Modal.getInstance(this.closest('.modal')).hide();">
                            <i class="fas fa-check"></i> Mô phỏng thành công
                        </button>
                        <button class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> Đóng
                        </button>
                    </div>
                `;
            }, 2000);

            // Auto remove modal after hide
            statusModal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(statusModal);
            });
        }

        function simulatePaymentSuccess() {
            if (!currentOrderId) {
                alert('Không có giao dịch nào để mô phỏng');
                return;
            }

            if (confirm('Mô phỏng thanh toán thành công cho đơn hàng: ' + currentOrderId + '?')) {
                // Đóng modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('qrModal'));
                modal.hide();

                // Hiển thị thông báo thành công
                setTimeout(() => {
                    alert('Mô phỏng thanh toán thành công! Trang sẽ được tải lại để cập nhật trạng thái.');
                    location.reload();
                }, 500);
            }
        }
    </script>

    <!-- Thêm thư viện QRCode.js -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
}
