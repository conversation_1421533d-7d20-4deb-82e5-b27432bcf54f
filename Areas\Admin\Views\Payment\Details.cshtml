@model MyMvcApp.Models.Payment

@{
    ViewData["Title"] = "Chi tiết thanh toán";
}


    
        <!-- Sidebar -->
        

        <!-- Main content -->
        
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">Chi tiết thanh toán</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                    </div>
                </div>
            </div>

            <div class="card">
                <div class="card-header bg-primary text-black">
                    <h5 style="text-color: black;" class="mb-0">Thông tin thanh toán #@Model.Id</h5>
                </div>
                <div class="card-body">
                    
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Thông tin cơ bản</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Mã thanh toán</dt>
                                        <dd class="col-sm-8">@Model.Id</dd>

                                        <dt class="col-sm-4">Bệnh nhân</dt>
                                        <dd class="col-sm-8">@Model.Patient?.FullName</dd>

                                        <dt class="col-sm-4">Dịch vụ</dt>
                                        <dd class="col-sm-8">@Model.Service?.Name</dd>

                                        <dt class="col-sm-4">Lịch hẹn</dt>
                                        <dd class="col-sm-8">
                                            @if (Model.Appointment != null)
                                            {
                                                <a asp-controller="Appointment" asp-action="Details" asp-route-id="@Model.AppointmentId">
                                                    @Model.Appointment.AppointmentDate.ToString("dd/MM/yyyy")
                                                </a>
                                            }
                                            else
                                            {
                                                <span>Không có</span>
                                            }
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Thông tin thanh toán</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Số tiền</dt>
                                        <dd class="col-sm-8">
                                            <span class="fw-bold">@Model.Amount.ToString("N0") VNĐ</span>
                                        </dd>

                                        <dt class="col-sm-4">Phương thức</dt>
                                        <dd class="col-sm-8">@Model.PaymentMethod</dd>

                                        <dt class="col-sm-4">Trạng thái</dt>
                                        <dd class="col-sm-8">
                                            <span class="badge @(Model.Status == "Completed" ? "bg-success" : 
                                                              Model.Status == "Pending" ? "bg-warning" : 
                                                              Model.Status == "Failed" ? "bg-danger" : "bg-info")">
                                                @Model.Status
                                            </span>
                                        </dd>

                                        <dt class="col-sm-4">Mã giao dịch</dt>
                                        <dd class="col-sm-8">@(string.IsNullOrEmpty(Model.TransactionId) ? "Không có" : Model.TransactionId)</dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>

                    
                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Thông tin thời gian</h6>
                                </div>
                                <div class="card-body">
                                    <dl class="row">
                                        <dt class="col-sm-4">Ngày thanh toán</dt>
                                        <dd class="col-sm-8">@Model.PaymentDate.ToString("dd/MM/yyyy HH:mm")</dd>

                                        <dt class="col-sm-4">Ngày tạo</dt>
                                        <dd class="col-sm-8">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>

                                        <dt class="col-sm-4">Cập nhật lần cuối</dt>
                                        <dd class="col-sm-8">
                                            @(Model.UpdatedAt.HasValue ? Model.UpdatedAt.Value.ToString("dd/MM/yyyy HH:mm") : "Chưa cập nhật")
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="card mb-3">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">Ghi chú</h6>
                                </div>
                                <div class="card-body">
                                    <p class="card-text">
                                        @if (string.IsNullOrEmpty(Model.Notes))
                                        {
                                            <em>Không có ghi chú</em>
                                        }
                                        else
                                        {
                                            @Model.Notes
                                        }
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                            <i class="fas fa-edit"></i> Chỉnh sửa
                        </a>
                        <a asp-action="Index" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Quay lại danh sách
                        </a>
                        <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Xóa
                        </a>
                    </div>
                </div>
            </div>
        
