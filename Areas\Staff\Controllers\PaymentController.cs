using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.Data;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.ViewModels.Admin;
using System.Threading.Tasks;

namespace MyMvcApp.Areas.Staff.Controllers
{
    [Area("Staff")]
    [Authorize(Roles = "Admin,Staff")]
    public class PaymentController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;
        private readonly ILogger<PaymentController> _logger;
        private readonly IMoMoService _momoService;

        public PaymentController(
            ApplicationDbContext context,
            IUserService userService,
            ILogger<PaymentController> logger,
            IMoMoService momoService)
        {
            _context = context;
            _userService = userService;
            _logger = logger;
            _momoService = momoService;
        }

        public async Task<IActionResult> Index()
        {
            var payments = await _context.PaymentTransactions
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Patient)
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Service)
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            return View(payments);
        }

        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var payment = await _context.PaymentTransactions
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Patient)
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Service)
                .Include(p => p.Appointment)
                    .ThenInclude(a => a.Dentist)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (payment == null)
            {
                return NotFound();
            }

            return View(payment);
        }

        public async Task<IActionResult> Create()
        {
            // Get appointments that don't have payments yet
            var unpaidAppointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.Status == "Completed" && !_context.PaymentTransactions.Any(p => p.AppointmentId == a.Id))
                .OrderByDescending(a => a.AppointmentDate)
                .ToListAsync();

            ViewBag.UnpaidAppointments = unpaidAppointments;
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(PaymentTransaction payment)
        {
            if (ModelState.IsValid)
            {
                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync(User);
                    if (currentUser != null)
                    {
                        payment.CreatedBy = currentUser.Id;
                        payment.CreatedAt = DateTime.Now;
                        payment.Status = "Pending";

                        // Get appointment details for amount calculation
                        var appointment = await _context.Appointments
                            .Include(a => a.Service)
                            .FirstOrDefaultAsync(a => a.Id == payment.AppointmentId);

                        if (appointment != null)
                        {
                            payment.Amount = appointment.Service?.Price ?? 0;
                        }

                        _context.PaymentTransactions.Add(payment);
                        await _context.SaveChangesAsync();

                        // Log activity
                        var activity = new Activity
                        {
                            Time = DateTime.Now,
                            Description = $"Tạo giao dịch thanh toán mới: {payment.Amount:C}",
                            UserId = currentUser.Id,
                            User = currentUser
                        };
                        _context.Activities.Add(activity);
                        await _context.SaveChangesAsync();

                        TempData["SuccessMessage"] = "Tạo giao dịch thanh toán thành công.";
                        return RedirectToAction(nameof(Index));
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating payment");
                    ModelState.AddModelError("", "Có lỗi xảy ra khi tạo giao dịch thanh toán. Vui lòng thử lại.");
                }
            }

            // Reload data if validation fails
            var unpaidAppointments = await _context.Appointments
                .Include(a => a.Patient)
                .Include(a => a.Service)
                .Include(a => a.Dentist)
                .Where(a => a.Status == "Completed" && !_context.PaymentTransactions.Any(p => p.AppointmentId == a.Id))
                .OrderByDescending(a => a.AppointmentDate)
                .ToListAsync();

            ViewBag.UnpaidAppointments = unpaidAppointments;
            return View(payment);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateStatus(int id, string status)
        {
            try
            {
                var payment = await _context.PaymentTransactions.FindAsync(id);
                if (payment == null)
                {
                    return Json(new { success = false, message = "Không tìm thấy giao dịch thanh toán" });
                }

                payment.Status = status;

                if (status == "Completed")
                {
                    payment.CompletedAt = DateTime.Now;
                }

                _context.Update(payment);
                await _context.SaveChangesAsync();

                // Log activity
                var currentUser = await _userService.GetCurrentUserAsync(User);
                if (currentUser != null)
                {
                    var activity = new Activity
                    {
                        Time = DateTime.Now,
                        Description = $"Cập nhật trạng thái thanh toán: {status}",
                        UserId = currentUser.Id,
                        User = currentUser
                    };
                    _context.Activities.Add(activity);
                    await _context.SaveChangesAsync();
                }

                return Json(new { success = true, message = "Cập nhật trạng thái thành công" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating payment status");
                return Json(new { success = false, message = "Có lỗi xảy ra khi cập nhật trạng thái" });
            }
        }

        public async Task<IActionResult> Statistics()
        {
            var today = DateTime.Today;
            var thisMonth = new DateTime(today.Year, today.Month, 1);
            var thisYear = new DateTime(today.Year, 1, 1);

            ViewBag.TodayRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue && p.CompletedAt.Value.Date == today)
                .SumAsync(p => p.Amount);

            ViewBag.MonthRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue && p.CompletedAt.Value >= thisMonth)
                .SumAsync(p => p.Amount);

            ViewBag.YearRevenue = await _context.PaymentTransactions
                .Where(p => p.Status == "Completed" && p.CompletedAt.HasValue && p.CompletedAt.Value >= thisYear)
                .SumAsync(p => p.Amount);

            ViewBag.PendingPayments = await _context.PaymentTransactions
                .CountAsync(p => p.Status == "Pending");

            return View();
        }
    }
}
