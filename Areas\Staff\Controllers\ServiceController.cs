using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using MyMvcApp.Data;
using System.Threading.Tasks;

namespace MyMvcApp.Areas.Staff.Controllers
{
    [Area("Staff")]
    [Authorize(Roles = "Admin,Staff")]
    public class ServiceController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;

        public ServiceController(ApplicationDbContext context, IUserService userService)
        {
            _context = context;
            _userService = userService;
        }

        public async Task<IActionResult> Index()
        {
            var services = await _context.Services
                .Include(s => s.ServiceCategory)
                .OrderByDescending(s => s.CreatedAt)
                .ToListAsync();

            // Load categories for dropdown
            ViewBag.ServiceCategories = await _context.ServiceCategories
                .Where(c => c.IsActive)
                .Select(c => new { c.Id, c.Name })
                .ToListAsync();

            return View(services);
        }

        public IActionResult Create()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Create(Service service)
        {
            if (ModelState.IsValid)
            {
                var currentUser = await _userService.GetCurrentUserAsync(User);
                if (currentUser != null)
                {
                    service.CreatedBy = currentUser.Id;
                    _context.Services.Add(service);
                    await _context.SaveChangesAsync();

                    // Log activity
                    var activity = new Activity
                    {
                        Time = DateTime.Now,
                        Description = $"Created new service: {service.Name}",
                        UserId = currentUser.Id,
                        User = currentUser
                    };
                    _context.Activities.Add(activity);
                    await _context.SaveChangesAsync();

                    return RedirectToAction(nameof(Index));
                }
            }
            return View(service);
        }

        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var service = await _context.Services.FindAsync(id);
            if (service == null)
            {
                return NotFound();
            }
            return View(service);
        }

        [HttpPost]
        public async Task<IActionResult> Edit(int id, Service service)
        {
            if (id != service.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    _context.Update(service);
                    await _context.SaveChangesAsync();

                    // Log activity
                    var currentUser = await _userService.GetCurrentUserAsync(User);
                    if (currentUser != null)
                    {
                        var activity = new Activity
                        {
                            Time = DateTime.Now,
                            Description = $"Updated service: {service.Name}",
                            UserId = currentUser.Id,
                            User = currentUser
                        };
                        _context.Activities.Add(activity);
                        await _context.SaveChangesAsync();
                    }
                }
                catch (Exception)
                {
                    if (!ServiceExists(service.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(service);
        }

        [HttpPost]
        public async Task<IActionResult> Delete(int id)
        {
            var service = await _context.Services.FindAsync(id);
            if (service != null)
            {
                _context.Services.Remove(service);
                await _context.SaveChangesAsync();

                // Log activity
                var currentUser = await _userService.GetCurrentUserAsync(User);
                if (currentUser != null)
                {
                    var activity = new Activity
                    {
                        Time = DateTime.Now,
                        Description = $"Deleted service: {service.Name}",
                        UserId = currentUser.Id,
                        User = currentUser
                    };
                    _context.Activities.Add(activity);
                    await _context.SaveChangesAsync();
                }
            }
            return RedirectToAction(nameof(Index));
        }

        public async Task<IActionResult> Categories()
        {
            var categories = await _context.ServiceCategories
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();

            return View(categories);
        }

        private bool ServiceExists(int id)
        {
            return _context.Services.Any(e => e.Id == id);
        }

        // AJAX Methods for dynamic service management
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateService(Service service, int durationMinutes, string isActiveParam = "")
        {
            try
            {
                if (durationMinutes <= 0)
                {
                    return Json(new { success = false, message = "Thời gian thực hiện phải lớn hơn 0 phút", errors = new[] { "Thời gian thực hiện phải lớn hơn 0 phút" } });
                }

                if (!service.ServiceCategoryId.HasValue || service.ServiceCategoryId <= 0)
                {
                    return Json(new { success = false, message = "Vui lòng chọn danh mục dịch vụ", errors = new[] { "Vui lòng chọn danh mục dịch vụ" } });
                }

                service.Duration = TimeSpan.FromMinutes(durationMinutes);
                service.IsActive = string.IsNullOrEmpty(isActiveParam) ? true : (!string.IsNullOrEmpty(isActiveParam) && (isActiveParam.ToLower() == "true" || isActiveParam.ToLower() == "on"));

                ModelState.Remove("Duration");
                ModelState.Remove("IsActive");

                if (service.ServiceCategoryId.HasValue && service.ServiceCategoryId > 0)
                {
                    var selectedCategory = await _context.ServiceCategories.FindAsync(service.ServiceCategoryId.Value);
                    if (selectedCategory != null)
                    {
                        service.Category = selectedCategory.Name;
                    }
                }

                if (string.IsNullOrEmpty(service.Category))
                {
                    service.Category = "Chưa phân loại";
                }

                service.CreatedAt = DateTime.Now;
                service.IsActive = true;

                if (ModelState.IsValid)
                {
                    _context.Services.Add(service);
                    await _context.SaveChangesAsync();
                    return Json(new { success = true });
                }

                var errors = ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage).ToList();
                return Json(new { success = false, message = "Dữ liệu không hợp lệ", errors = errors });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "Có lỗi xảy ra: " + ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetService(int id)
        {
            var service = await _context.Services.FindAsync(id);
            if (service == null)
            {
                return Json(new { success = false, message = "Không tìm thấy dịch vụ" });
            }
            return Json(new {
                success = true,
                id = service.Id,
                name = service.Name,
                description = service.Description,
                price = service.Price,
                duration = service.Duration.TotalMinutes,
                category = service.Category,
                serviceCategoryId = service.ServiceCategoryId,
                isActive = service.IsActive
            });
        }
    }
}
