@{
    ViewData["Title"] = "T<PERSON><PERSON> cập bị từ chối";
    Layout = null;
}

<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"] - Nha <PERSON> 3B</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s ease-in-out infinite;
            z-index: 1;
        }

        @@keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .access-denied-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
            position: relative;
            z-index: 10;
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }

        .error-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.3);
            animation: pulse 2s infinite;
        }

        .error-icon i {
            font-size: 3rem;
            color: white;
        }

        @@keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .error-title {
            color: #2c3e50;
            font-weight: 700;
            font-size: 2rem;
            margin-bottom: 1rem;
        }

        .error-message {
            color: #7f8c8d;
            font-weight: 400;
            font-size: 1.1rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .error-details {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 2rem;
            color: #856404;
        }

        .btn-home {
            background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(74, 159, 159, 0.3);
            margin: 0.5rem;
        }

        .btn-home:hover {
            background: linear-gradient(135deg, #2E7D7D 0%, #1a5a5a 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(74, 159, 159, 0.4);
            color: white;
            text-decoration: none;
        }

        .btn-login {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            border: none;
            border-radius: 12px;
            padding: 0.75rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(116, 185, 255, 0.3);
            margin: 0.5rem;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #0984e3 0%, #0056b3 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
            color: white;
            text-decoration: none;
        }

        /* Responsive */
        @@media (max-width: 576px) {
            .access-denied-container {
                margin: 1rem;
                padding: 2rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="access-denied-container">
        <div class="error-icon">
            <i class="fas fa-ban"></i>
        </div>
        
        <h1 class="error-title">Truy cập bị từ chối</h1>
        
        <p class="error-message">
            Xin lỗi, bạn không có quyền truy cập vào trang này. 
            Vui lòng liên hệ quản trị viên nếu bạn cho rằng đây là lỗi.
        </p>

        <div class="error-details">
            <strong><i class="fas fa-info-circle me-2"></i>Thông tin:</strong><br>
            Trang này yêu cầu quyền Admin, Staff hoặc Dentist để truy cập.
        </div>

        <div class="mt-4">
            <a href="@Url.Action("Login", "Account")" class="btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>
                Đăng nhập lại
            </a>
            
            <a href="@Url.Action("Landing", "Home")" class="btn-home">
                <i class="fas fa-home me-2"></i>
                Về trang chủ
            </a>
        </div>

        <div class="mt-4">
            <small class="text-muted">
                <i class="fas fa-phone me-2"></i>
                Cần hỗ trợ? Liên hệ: <strong>0123-456-789</strong>
            </small>
        </div>
    </div>

    <!-- Bootstrap JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Add floating animation to container
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.access-denied-container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.5s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
