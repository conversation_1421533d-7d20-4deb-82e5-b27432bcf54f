@using Microsoft.AspNetCore.Identity
@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager

@{
    var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
    var currentAction = ViewContext.RouteData.Values["action"]?.ToString();
    var currentArea = ViewContext.RouteData.Values["area"]?.ToString();
}

<style>
    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        width: 250px;
        background: linear-gradient(180deg, #1976d2 0%, #1565c0 100%);
        color: white;
        z-index: 1000;
        overflow-y: auto;
        box-shadow: 2px 0 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .sidebar-header {
        padding: 1.5rem 1rem;
        border-bottom: 1px solid rgba(255,255,255,0.1);
        text-align: center;
    }

    .sidebar-header h4 {
        margin: 0;
        font-weight: 700;
        font-size: 1.2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .sidebar-header .badge {
        background: rgba(255,255,255,0.2);
        color: white;
        font-size: 0.75rem;
        margin-top: 0.5rem;
    }

    .sidebar-nav {
        padding: 1rem 0;
    }

    .nav-item {
        margin: 0.25rem 0.75rem;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 0.75rem 1rem;
        color: rgba(255,255,255,0.9) !important;
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }

    .nav-link:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        transition: left 0.5s;
    }

    .nav-link:hover:before {
        left: 100%;
    }

    .nav-link:hover {
        color: white !important;
        background: rgba(255,255,255,0.1);
        transform: translateX(5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    }

    .nav-link.active {
        background: rgba(255,255,255,0.2);
        color: white !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.3);
    }

    .nav-link i {
        width: 20px;
        margin-right: 0.75rem;
        text-align: center;
        font-size: 1.1rem;
    }

    .sidebar-footer {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 1rem;
        border-top: 1px solid rgba(255,255,255,0.1);
        background: rgba(0,0,0,0.1);
    }

    .user-info {
        display: flex;
        align-items: center;
        padding: 0.5rem;
        background: rgba(255,255,255,0.1);
        border-radius: 8px;
        margin-bottom: 0.5rem;
    }

    .user-avatar {
        width: 32px;
        height: 32px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
    }

    .user-details {
        flex: 1;
        min-width: 0;
    }

    .user-name {
        font-weight: 600;
        font-size: 0.9rem;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .user-role {
        font-size: 0.75rem;
        opacity: 0.8;
        margin: 0;
    }

    @@media (max-width: 768px) {
        .sidebar {
            transform: translateX(-100%);
        }

        .sidebar.show {
            transform: translateX(0);
        }
    }
</style>

<div class="sidebar">
    <div class="sidebar-header">
        <h4>
            <i class="fas fa-clipboard-list me-2"></i>
            Staff Panel
        </h4>
        <span class="badge">Nhân viên lễ tân</span>
    </div>

    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Home" && currentAction == "Index" ? "active" : "")"
                   asp-area="Staff" asp-controller="Home" asp-action="Index">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
            </li>

            <!-- Appointments -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Appointment" && currentAction == "Calendar" ? "active" : "")"
                   asp-area="Staff" asp-controller="Appointment" asp-action="Calendar">
                    <i class="fas fa-calendar-alt"></i>
                    <span>Lịch hẹn</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Appointment" && currentAction == "Index" ? "active" : "")"
                   asp-area="Staff" asp-controller="Appointment" asp-action="Index">
                    <i class="fas fa-list"></i>
                    <span>Danh sách lịch hẹn</span>
                </a>
            </li>

            <!-- Patients -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Patient" ? "active" : "")"
                   asp-area="Staff" asp-controller="Patient" asp-action="Index">
                    <i class="fas fa-user-injured"></i>
                    <span>Quản lý bệnh nhân</span>
                </a>
            </li>

            <!-- Services -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Service" ? "active" : "")"
                   asp-area="Staff" asp-controller="Service" asp-action="Index">
                    <i class="fas fa-tooth"></i>
                    <span>Quản lý dịch vụ</span>
                </a>
            </li>

            <!-- Payments -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Payment" ? "active" : "")"
                   asp-area="Staff" asp-controller="Payment" asp-action="Index">
                    <i class="fas fa-credit-card"></i>
                    <span>Quản lý thanh toán</span>
                </a>
            </li>

            <!-- Reports -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Report" ? "active" : "")"
                   asp-area="Staff" asp-controller="Report" asp-action="Index">
                    <i class="fas fa-chart-bar"></i>
                    <span>Báo cáo doanh thu</span>
                </a>
            </li>

            <!-- Role Test -->
            <li class="nav-item">
                <a class="nav-link @(currentArea == "Staff" && currentController == "Home" && currentAction == "RoleTest" ? "active" : "")"
                   asp-area="Staff" asp-controller="Home" asp-action="RoleTest">
                    <i class="fas fa-shield-alt"></i>
                    <span>Kiểm tra phân quyền</span>
                </a>
            </li>
        </ul>
    </nav>

    <div class="sidebar-footer">
        @if (SignInManager.IsSignedIn(User))
        {
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <p class="user-name">@User.Identity?.Name</p>
                    <p class="user-role">Staff</p>
                </div>
            </div>
        }
        
        <a class="nav-link" asp-area="" asp-controller="Account" asp-action="Logout">
            <i class="fas fa-sign-out-alt"></i>
            <span>Đăng xuất</span>
        </a>
    </div>
</div>
