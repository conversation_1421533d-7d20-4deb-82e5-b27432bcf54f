@{
    ViewData["Title"] = "Dentist Dashboard";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-user-md me-2"></i>
                    Dashboard - Bác sĩ nha khoa
                </h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <div class="btn-group me-2">
                        <a href="@Url.Action("Calendar", "Appointment")" class="btn btn-primary">
                            <i class="fas fa-calendar-alt me-1"></i>Xem lịch hẹn
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Message -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5 class="alert-heading">
                    <i class="fas fa-info-circle me-2"></i><PERSON><PERSON><PERSON> mừng, <PERSON><PERSON><PERSON>!
                </h5>
                <p class="mb-0">
                    Đây là bảng điều khiển dành riêng cho bác sĩ. Bạn có thể xem lịch hẹn của mình, 
                    cập nhật trạng thái điều trị và xem thông tin bệnh nhân.
                </p>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Lịch hẹn hôm nay
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.MyTodayAppointments</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Lịch hẹn tuần này
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.MyThisWeekAppointments</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-week fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Đã hoàn thành
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.MyCompletedAppointments</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Tổng bệnh nhân
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@ViewBag.MyPatientsCount</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-bolt me-2"></i>Thao tác nhanh
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Calendar", "Appointment")" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-calendar-alt fa-2x mb-2"></i><br>
                                Xem lịch hẹn
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Index", "Patient")" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-user-injured fa-2x mb-2"></i><br>
                                Xem bệnh nhân
                            </a>
                        </div>
                        <div class="col-md-4 mb-3">
                            <a href="@Url.Action("Index", "Service")" class="btn btn-info btn-lg w-100">
                                <i class="fas fa-tooth fa-2x mb-2"></i><br>
                                Xem dịch vụ
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Appointments -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-history me-2"></i>Lịch hẹn gần đây của tôi
                    </h6>
                </div>
                <div class="card-body">
                    @if (ViewBag.MyRecentAppointments != null && ((IEnumerable<dynamic>)ViewBag.MyRecentAppointments).Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Ngày</th>
                                        <th>Giờ</th>
                                        <th>Bệnh nhân</th>
                                        <th>Dịch vụ</th>
                                        <th>Trạng thái</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var appointment in (IEnumerable<dynamic>)ViewBag.MyRecentAppointments)
                                    {
                                        <tr>
                                            <td>@appointment.Date.ToString("dd/MM/yyyy")</td>
                                            <td>@appointment.Time.ToString(@"hh\:mm")</td>
                                            <td>@appointment.PatientName</td>
                                            <td>@appointment.ServiceName</td>
                                            <td>
                                                @switch (appointment.Status)
                                                {
                                                    case "Completed":
                                                        <span class="badge bg-success">Hoàn thành</span>
                                                        break;
                                                    case "Cancelled":
                                                        <span class="badge bg-danger">Đã hủy</span>
                                                        break;
                                                    case "No-show":
                                                        <span class="badge bg-secondary">Không đến</span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-primary">Đã đặt</span>
                                                        break;
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Chưa có lịch hẹn nào được ghi nhận</p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }
    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }
    .border-left-info {
        border-left: 0.25rem solid #36b9cc !important;
    }
    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }
    .text-gray-300 {
        color: #dddfeb !important;
    }
    .text-gray-800 {
        color: #5a5c69 !important;
    }
</style>
