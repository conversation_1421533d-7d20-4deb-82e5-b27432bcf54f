using Microsoft.AspNetCore.Identity;
using System.ComponentModel.DataAnnotations;

namespace MyMvcApp.Models
{
    public class ApplicationUser : IdentityUser
    {
        [Required]
        [Display(Name = "<PERSON>ọ tên")]
        public string FullName { get; set; } = string.Empty;

        [Display(Name = "Đ<PERSON><PERSON> chỉ")]
        public string? Address { get; set; }

        [Required]
        [Display(Name = "<PERSON>à<PERSON> sinh")]
        [DataType(DataType.Date)]
        public DateOnly DateOfBirth { get; set; }

        [Required]
        [Display(Name = "Giới tính")]
        public string Gender { get; set; } = string.Empty;

        [Display(Name = "<PERSON><PERSON><PERSON> tạo")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        [Display(Name = "Trạng thái")]
        public bool IsActive { get; set; } = true;

        public virtual ICollection<IdentityRole> Roles { get; set; } = new List<IdentityRole>();
    }
} 