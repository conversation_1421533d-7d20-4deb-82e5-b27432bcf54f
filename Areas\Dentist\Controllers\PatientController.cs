using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MyMvcApp.Data;
using MyMvcApp.Models;
using MyMvcApp.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using System.Threading.Tasks;

namespace MyMvcApp.Areas.Dentist.Controllers
{
    [Area("Dentist")]
    [Authorize(Roles = "Admin,Dentist")]
    public class PatientController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IUserService _userService;
        private readonly ILogger<PatientController> _logger;

        public PatientController(
            ApplicationDbContext context,
            IUserService userService,
            ILogger<PatientController> logger)
        {
            _context = context;
            _userService = userService;
            _logger = logger;
        }

        // GET: Patient - Only show patients that have appointments with this dentist
        public async Task<IActionResult> Index()
        {
            var currentUser = await _userService.GetCurrentUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToAction("Login", "Account");
            }

            // Get patients who have appointments with this dentist
            var patients = await _context.Patients
                .Where(p => _context.Appointments.Any(a => a.PatientId == p.Id && a.DentistId == currentUser.Id))
                .OrderByDescending(p => p.CreatedAt)
                .ToListAsync();

            ViewBag.IsReadOnly = true; // Flag to indicate read-only mode
            return View(patients);
        }

        // GET: Patient/Details/5 - Only show details for patients with appointments
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var currentUser = await _userService.GetCurrentUserAsync(User);
            if (currentUser == null)
            {
                return RedirectToAction("Login", "Account");
            }

            // Check if this dentist has appointments with this patient
            var hasAppointment = await _context.Appointments
                .AnyAsync(a => a.PatientId == id && a.DentistId == currentUser.Id);

            if (!hasAppointment)
            {
                TempData["ErrorMessage"] = "Bạn chỉ có thể xem thông tin bệnh nhân mà bạn có lịch hẹn.";
                return RedirectToAction(nameof(Index));
            }

            var patient = await _context.Patients
                .FirstOrDefaultAsync(m => m.Id == id);
            
            if (patient == null)
            {
                return NotFound();
            }

            // Get appointment history with this dentist
            var appointmentHistory = await _context.Appointments
                .Include(a => a.Service)
                .Where(a => a.PatientId == id && a.DentistId == currentUser.Id)
                .OrderByDescending(a => a.AppointmentDate)
                .ToListAsync();

            ViewBag.AppointmentHistory = appointmentHistory;
            ViewBag.IsReadOnly = true;
            
            return View(patient);
        }

        // Dentist cannot create, edit, or delete patients
        public IActionResult Create()
        {
            TempData["ErrorMessage"] = "Bạn không có quyền tạo bệnh nhân mới. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }

        public IActionResult Edit(int? id)
        {
            TempData["ErrorMessage"] = "Bạn không có quyền chỉnh sửa thông tin bệnh nhân. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }

        public IActionResult Delete(int? id)
        {
            TempData["ErrorMessage"] = "Bạn không có quyền xóa bệnh nhân. Vui lòng liên hệ nhân viên lễ tân.";
            return RedirectToAction(nameof(Index));
        }
    }
}
