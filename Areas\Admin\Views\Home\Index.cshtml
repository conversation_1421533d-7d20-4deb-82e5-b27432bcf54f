@{
    ViewData["Title"] = "Dashboard";
}

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-4 mb-4">
    <div>
        <h1 class="h2 fw-bold text-primary mb-2">
            <i class="fas fa-chart-line me-3 text-primary"></i>Dashboard Quản Lý
        </h1>
        <p class="text-muted mb-0">Tổng quan hoạt động hệ thống nha khoa</p>
    </div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-primary">
                <i class="fas fa-download me-2"></i>Xuất báo cáo
            </button>
            <button type="button" class="btn btn-outline-primary">
                <i class="fas fa-refresh me-2"></i>Làm mới
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row g-4 mb-5">
    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-lg h-100 overflow-hidden position-relative">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-primary fw-bold text-uppercase mb-2" style="font-size: 0.85rem; letter-spacing: 1px;">
                            <i class="fas fa-users me-2"></i>Tổng số bệnh nhân
                        </div>
                        <div class="h3 mb-2 fw-bold text-dark">@ViewBag.TotalPatients</div>
                        <div class="text-muted small">
                            <i class="fas fa-arrow-up text-success me-1"></i>
                            <span class="text-success fw-semibold">12%</span> so với tháng trước
                        </div>
                    </div>
                    <div class="text-primary opacity-75">
                        <i class="fas fa-users fa-3x"></i>
                    </div>
                </div>
            </div>
            <div class="position-absolute bottom-0 start-0 w-100" style="height: 4px; background: linear-gradient(90deg, #4A9F9F 0%, #5dade2 100%);"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-lg h-100 overflow-hidden position-relative">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-success fw-bold text-uppercase mb-2" style="font-size: 0.85rem; letter-spacing: 1px;">
                            <i class="fas fa-calendar-check me-2"></i>Lịch hẹn hôm nay
                        </div>
                        <div class="h3 mb-2 fw-bold text-dark">@ViewBag.TodayAppointments</div>
                        <div class="text-muted small">
                            <i class="fas fa-clock text-info me-1"></i>
                            <span class="text-info fw-semibold">5 lịch</span> đang chờ xác nhận
                        </div>
                    </div>
                    <div class="text-success opacity-75">
                        <i class="fas fa-calendar-check fa-3x"></i>
                    </div>
                </div>
            </div>
            <div class="position-absolute bottom-0 start-0 w-100" style="height: 4px; background: linear-gradient(90deg, #28a745 0%, #20c997 100%);"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-lg h-100 overflow-hidden position-relative">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-info fw-bold text-uppercase mb-2" style="font-size: 0.85rem; letter-spacing: 1px;">
                            <i class="fas fa-tooth me-2"></i>Tổng số dịch vụ
                        </div>
                        <div class="h3 mb-2 fw-bold text-dark">@ViewBag.TotalServices</div>
                        <div class="text-muted small">
                            <i class="fas fa-plus text-success me-1"></i>
                            <span class="text-success fw-semibold">3 dịch vụ</span> mới tuần này
                        </div>
                    </div>
                    <div class="text-info opacity-75">
                        <i class="fas fa-tooth fa-3x"></i>
                    </div>
                </div>
            </div>
            <div class="position-absolute bottom-0 start-0 w-100" style="height: 4px; background: linear-gradient(90deg, #17a2b8 0%, #6f42c1 100%);"></div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card border-0 shadow-lg h-100 overflow-hidden position-relative">
            <div class="card-body p-4">
                <div class="d-flex align-items-center justify-content-between">
                    <div>
                        <div class="text-warning fw-bold text-uppercase mb-2" style="font-size: 0.85rem; letter-spacing: 1px;">
                            <i class="fas fa-user-md me-2"></i>Tổng số người dùng
                        </div>
                        <div class="h3 mb-2 fw-bold text-dark">@ViewBag.TotalUsers</div>
                        <div class="text-muted small">
                            <i class="fas fa-user-plus text-primary me-1"></i>
                            <span class="text-primary fw-semibold">2 người</span> đăng ký mới
                        </div>
                    </div>
                    <div class="text-warning opacity-75">
                        <i class="fas fa-user-md fa-3x"></i>
                    </div>
                </div>
            </div>
            <div class="position-absolute bottom-0 start-0 w-100" style="height: 4px; background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%);"></div>
        </div>
    </div>
</div>

<!-- Recent Activities Section -->
<div class="row g-4">
    <div class="col-md-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 fw-bold text-primary">
                    <i class="fas fa-history me-2"></i>Hoạt động gần đây
                </h6>
                <div class="dropdown">
                    <button class="btn btn-outline-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-filter me-1"></i>Lọc
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-day me-2"></i>Hôm nay</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-week me-2"></i>Tuần này</a></li>
                        <li><a class="dropdown-item" href="#"><i class="fas fa-calendar-alt me-2"></i>Tháng này</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="activityTable">
                        <thead class="table-light">
                            <tr>
                                <th><i class="fas fa-clock me-2"></i>Thời gian</th>
                                <th><i class="fas fa-info-circle me-2"></i>Mô tả</th>
                                <th><i class="fas fa-user me-2"></i>Người thực hiện</th>
                                <th><i class="fas fa-tag me-2"></i>Loại hoạt động</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (ViewBag.RecentActivities != null)
                            {
                                @foreach (var activity in ViewBag.RecentActivities)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <div class="fw-bold">@activity.Time.ToString("dd/MM/yyyy")</div>
                                                    <small class="text-muted">@activity.Time.ToString("HH:mm")</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>
                                                @activity.Description
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-2">
                                                    <i class="fas fa-user-circle"></i>
                                                </div>
                                                <span class="badge bg-info text-white">@activity.User</span>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-cogs me-1"></i>Hệ thống
                                            </span>
                                        </td>
                                    </tr>
                                }
                            }
                            else
                            {
                                <tr>
                                    <td colspan="4" class="text-center text-muted py-5">
                                        <div>
                                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">Không có hoạt động nào gần đây</h5>
                                            <p class="text-muted">Các hoạt động mới sẽ được hiển thị tại đây</p>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function () {
            // Check if DataTable is already initialized
            if ($.fn.DataTable.isDataTable('#activityTable')) {
                $('#activityTable').DataTable().destroy();
            }

            // Initialize DataTable for activity log
            $('#activityTable').DataTable({
                language: {
                    url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
                },
                order: [[0, 'desc']], // Sort by time descending
                pageLength: 10,
                responsive: true,
                columnDefs: [
                    { orderable: false, targets: [3] } // Disable sorting for "Loại hoạt động" column
                ]
            });
        });
    </script>
}
