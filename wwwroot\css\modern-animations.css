/* Modern Animations CSS - Placeholder file to prevent 404 errors */

/* Basic fade-in animation */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Smooth transitions for interactive elements */
.btn, .card, .modal {
    transition: all 0.3s ease;
}

/* Hover effects for cards */
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Modal animations */
.modal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

.modal.show .modal-dialog {
    transform: none;
}

/* Button hover effects */
.btn:hover {
    transform: translateY(-1px);
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading spinner */
.spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}
