@model MyMvcApp.ViewModels.UserChangePasswordViewModel
@{
    ViewData["Title"] = "Đ<PERSON><PERSON> mật khẩu";
    Layout = "_UserLayout";
}

@section Styles {
    <style>
        .profile-container {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .profile-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .profile-header {
            background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            font-size: 3rem;
        }
        
        .profile-body {
            padding: 40px;
        }
        
        .form-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .section-title {
            color: #4A9F9F;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating input {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .form-floating input:focus {
            border-color: #4A9F9F;
            box-shadow: 0 0 0 0.2rem rgba(74, 159, 159, 0.25);
        }
        
        .action-buttons {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #4A9F9F 0%, #2E7D7D 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 25px;
            color: white;
            text-decoration: none;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(74, 159, 159, 0.3);
            color: white;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            border: 1px solid #4A9F9F;
            border-radius: 10px;
            color: #155724;
            margin-bottom: 20px;
        }
        
        .password-requirements {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
            border-left: 4px solid #4A9F9F;
        }
        
        .password-requirements ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .password-requirements li {
            margin-bottom: 5px;
            color: #6c757d;
        }
        
        @@media (max-width: 768px) {
            .profile-body {
                padding: 20px;
            }
            
            .btn-custom {
                display: block;
                margin: 10px auto;
                width: 100%;
                max-width: 250px;
            }
        }
    </style>
}

<div class="profile-container">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="profile-card">
                    <div class="profile-header">
                        <div class="profile-avatar">
                            <i class="fas fa-key"></i>
                        </div>
                        <h2>Đổi mật khẩu</h2>
                        <p class="mb-0">Thay đổi mật khẩu để bảo mật tài khoản</p>
                    </div>
                    
                    <div class="profile-body">
                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                @TempData["SuccessMessage"]
                            </div>
                        }
                        
                        <form id="changePasswordForm" asp-action="ChangePassword" method="post">
                            <div asp-validation-summary="ModelOnly" class="alert alert-danger" role="alert"></div>
                            
                            <div class="form-section">
                                <h4 class="section-title">
                                    <i class="fas fa-lock me-2"></i>Thông tin mật khẩu
                                </h4>
                                
                                <div class="form-floating">
                                    <input asp-for="CurrentPassword" class="form-control" placeholder="Mật khẩu hiện tại" type="password" />
                                    <label asp-for="CurrentPassword">Mật khẩu hiện tại</label>
                                    <span asp-validation-for="CurrentPassword" class="text-danger"></span>
                                </div>
                                
                                <div class="form-floating">
                                    <input asp-for="NewPassword" class="form-control" placeholder="Mật khẩu mới" type="password" />
                                    <label asp-for="NewPassword">Mật khẩu mới</label>
                                    <span asp-validation-for="NewPassword" class="text-danger"></span>
                                </div>
                                
                                <div class="password-requirements">
                                    <strong>Yêu cầu mật khẩu:</strong>
                                    <ul>
                                        <li>Ít nhất 8 ký tự</li>
                                        <li>Có ít nhất 1 chữ hoa (A-Z)</li>
                                        <li>Có ít nhất 1 chữ thường (a-z)</li>
                                        <li>Có ít nhất 1 số (0-9)</li>
                                        <li>Có ít nhất 1 ký tự đặc biệt (@@$$!%*?&)</li>
                                    </ul>
                                </div>
                                
                                <div class="form-floating">
                                    <input asp-for="ConfirmPassword" class="form-control" placeholder="Xác nhận mật khẩu mới" type="password" />
                                    <label asp-for="ConfirmPassword">Xác nhận mật khẩu mới</label>
                                    <span asp-validation-for="ConfirmPassword" class="text-danger"></span>
                                </div>
                            </div>
                        </form>
                        
                        <!-- Action Buttons -->
                        <div class="action-buttons">
                            <button type="submit" form="changePasswordForm" class="btn-custom">
                                <i class="fas fa-save me-2"></i>Đổi mật khẩu
                            </button>
                            @if (User.IsInRole("Admin"))
                            {
                                <a href="@Url.Action("Index", "Home", new { area = "Admin" })" class="btn-custom">
                                    <i class="fas fa-tachometer-alt me-2"></i>Về Admin Dashboard
                                </a>
                            }
                            else
                            {
                                <a href="@Url.Action("MyProfile", "Home")" class="btn-custom">
                                    <i class="fas fa-arrow-left me-2"></i>Quay lại
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function() {
            // Add fade-in animation
            $('.profile-card').hide().fadeIn(800);
            
            // Password strength indicator
            $('input[name="NewPassword"]').on('input', function() {
                var password = $(this).val();
                var requirements = $('.password-requirements li');
                
                // Reset all requirements
                requirements.removeClass('text-success text-danger').addClass('text-muted');
                
                if (password.length >= 8) {
                    requirements.eq(0).removeClass('text-muted').addClass('text-success');
                } else {
                    requirements.eq(0).removeClass('text-muted').addClass('text-danger');
                }
                
                if (/[A-Z]/.test(password)) {
                    requirements.eq(1).removeClass('text-muted').addClass('text-success');
                } else {
                    requirements.eq(1).removeClass('text-muted').addClass('text-danger');
                }
                
                if (/[a-z]/.test(password)) {
                    requirements.eq(2).removeClass('text-muted').addClass('text-success');
                } else {
                    requirements.eq(2).removeClass('text-muted').addClass('text-danger');
                }
                
                if (/[0-9]/.test(password)) {
                    requirements.eq(3).removeClass('text-muted').addClass('text-success');
                } else {
                    requirements.eq(3).removeClass('text-muted').addClass('text-danger');
                }
                
                if (/[@@$$!%*?&]/.test(password)) {
                    requirements.eq(4).removeClass('text-muted').addClass('text-success');
                } else {
                    requirements.eq(4).removeClass('text-muted').addClass('text-danger');
                }
            });
        });
    </script>
}
