@{
    ViewData["Title"] = "Kiểm tra phân quyền - Staff";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2">
                    <i class="fas fa-shield-alt me-2"></i>
                    Kiểm tra phân quyền - Staff Panel
                </h1>
            </div>

            <div class="row">
                <!-- Current User Info -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-user me-2"></i>Thông tin người dùng hiện tại
                            </h5>
                        </div>
                        <div class="card-body">
                            <p><strong>Tên đăng nhập:</strong> @(User.Identity?.Name ?? "Chưa đăng nhập")</p>
                            <p><strong>Đã xác thực:</strong> 
                                @if (User.Identity?.IsAuthenticated == true)
                                {
                                    <span class="badge bg-success">Có</span>
                                }
                                else
                                {
                                    <span class="badge bg-danger">Không</span>
                                }
                            </p>
                            <p><strong>Vai trò:</strong></p>
                            <ul class="list-unstyled">
                                @if (User.IsInRole("Admin"))
                                {
                                    <li><span class="badge bg-danger me-1">Admin</span> Quản trị viên</li>
                                }
                                @if (User.IsInRole("Staff"))
                                {
                                    <li><span class="badge bg-primary me-1">Staff</span> Nhân viên lễ tân</li>
                                }
                                @if (User.IsInRole("Dentist"))
                                {
                                    <li><span class="badge bg-info me-1">Dentist</span> Bác sĩ nha khoa</li>
                                }
                                @if (User.IsInRole("User"))
                                {
                                    <li><span class="badge bg-secondary me-1">User</span> Người dùng</li>
                                }
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Staff Permission Matrix -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-clipboard-list me-2"></i>Quyền hạn Staff
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Chức năng</th>
                                            <th class="text-center">Quyền</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Xem và tạo lịch hẹn</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Quản lý bệnh nhân</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Quản lý dịch vụ</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Quản lý thanh toán</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Xem báo cáo doanh thu</td>
                                            <td class="text-center">
                                                <i class="fas fa-check text-success"></i>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Quản lý người dùng</td>
                                            <td class="text-center">
                                                <i class="fas fa-times text-danger"></i>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-bolt me-2"></i>Kiểm tra chức năng
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Calendar", "Appointment")" class="btn btn-primary btn-lg w-100">
                                        <i class="fas fa-calendar-alt fa-2x mb-2"></i><br>
                                        Lịch hẹn
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Index", "Patient")" class="btn btn-success btn-lg w-100">
                                        <i class="fas fa-user-injured fa-2x mb-2"></i><br>
                                        Bệnh nhân
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Index", "Service")" class="btn btn-info btn-lg w-100">
                                        <i class="fas fa-tooth fa-2x mb-2"></i><br>
                                        Dịch vụ
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Index", "Payment")" class="btn btn-warning btn-lg w-100">
                                        <i class="fas fa-credit-card fa-2x mb-2"></i><br>
                                        Thanh toán
                                    </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Index", "Report")" class="btn btn-secondary btn-lg w-100">
                                        <i class="fas fa-chart-bar fa-2x mb-2"></i><br>
                                        Báo cáo
                                    </a>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <a href="@Url.Action("Index", "UserManagement", new { area = "Admin" })" class="btn btn-danger btn-lg w-100 disabled">
                                        <i class="fas fa-users fa-2x mb-2"></i><br>
                                        Người dùng (Không có quyền)
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Area Information -->
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info">
                        <h5 class="alert-heading">
                            <i class="fas fa-info-circle me-2"></i>Thông tin Area
                        </h5>
                        <p class="mb-1">
                            <strong>Current Area:</strong> Staff<br>
                            <strong>Layout:</strong> _StaffLayout.cshtml<br>
                            <strong>Sidebar:</strong> _StaffSidebar.cshtml<br>
                            <strong>Theme Color:</strong> Blue (#1976d2)
                        </p>
                        <hr>
                        <p class="mb-0">
                            Bạn đang truy cập Staff Area với đầy đủ quyền quản lý lịch hẹn, bệnh nhân, dịch vụ, thanh toán và báo cáo.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
